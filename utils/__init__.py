import os
import secrets
import shutil
import mimetypes
import re
from urllib.parse import urlparse

from django.conf import settings
from django.core.files import File
from django.http import HttpRequest
from django.core.mail import send_mail

from rest_framework import serializers, status
from rest_framework.generics import GenericAPIView
from rest_framework.parsers import <PERSON><PERSON>artPars<PERSON>, FormParser
from rest_framework.response import Response
from unidecode import unidecode
from django.utils.text import slugify
import random
import string

from django.conf import settings

from django.utils.translation import gettext_lazy as _

from cachetools.func import lru_cache
from django.http import HttpRequest
from django.contrib import admin

# Moved filer imports to avoid circular imports
# These will be imported when needed in functions

@lru_cache
def qs_thumbs():
    from filer.models import ThumbnailOption
    return ThumbnailOption.objects.all()


def get_thumbs(obj, request: HttpRequest = None) -> dict:
    print(f'----> {obj}')
    if not obj:
        return {}

    try:
        from easy_thumbnails.files import get_thumbnailer
        
        thumbnail_object = {}
        thumbs = qs_thumbs()
        print(f'--> {thumbs}')
        
        # بررسی نوع فیلد و استفاده از روش مناسب
        if hasattr(obj, 'easy_thumbnails_thumbnailer'):
            # برای فیلدهای FilerImageField
            thumbnailer = obj.easy_thumbnails_thumbnailer
        else:
            # برای فیلدهای ImageField معمولی
            thumbnailer = get_thumbnailer(obj)
        
        for thumb in thumbs:
            url = thumbnailer.get_thumbnail(thumb.as_dict).url
            if request:
                url = request.build_absolute_uri(url)

            thumbnail_object[thumb.name] = url

        return thumbnail_object

    except Exception as p:
        print(p)
        return {}


def environment_callback(request):
    if settings.DEBUG:
        return [_("Development"), "primary"]

    return [_("Production"), "primary"]



def send_email(recipient, code):
    send_mail(
        'Test Email',
        f'This is a test email {code} from Django using Gmail SMTP.',
        '<EMAIL>', 
        recipient,  
        fail_silently=False,
    )    
    return True


def is_valid_email(email):
    # تعریف الگوی regex برای یک ایمیل معتبر
    email_regex = r'^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+$'
    
    # بررسی اینکه آیا ایمیل با regex مطابقت دارد یا خیر
    if re.match(email_regex, email):
        return True
    return False


def generate_slug_for_model(model, value: str, recycled_count: int = 0):
    from slugify import slugify
    try:

        base_slug = slugify(unidecode(value))
        slug = base_slug
        if recycled_count > 0:
            slug = f"{base_slug}-{recycled_count}"
    
        if model.objects.filter(slug=slug).exists():
            return generate_slug_for_model(model, value, recycled_count + 1)
    
        return slug[:50]
    except Exception as exp:
        letters = string.ascii_lowercase
        result_str = ''.join(random.choice(letters) for i in range(8))
        return result_str


def generate_slugen_for_model(model, value_en, value_pk):
    try:
        unique_slug = value_en
        if not value_pk or not value_en:
            base_slug = slugify(unidecode(value_en))
            unique_slug = base_slug
            num = 1
            while model.objects.filter(slug=unique_slug).exists():
                unique_slug = f"{base_slug}-{num}"
                num += 1
                
        return unique_slug
    except Exception as exp:
        letters = string.ascii_lowercase
        result_str = ''.join(random.choice(letters) for i in range(8))
        return result_str


def exclude_host_from_url(url):
    # Parse the URL
    parsed_url = urlparse(url)

    # Extract the path and query parameters
    path_with_query = parsed_url.path + parsed_url.query

    return path_with_query


def generate_slug_for_model(model, value: str, recycled_count: int = 0):
    from slugify import slugify

    slug = slugify(value)
    if model.objects.filter(slug=slug).exists():
        recycled_count += 1
        if value.endswith(f'-{recycled_count - 1}'):
            value = value.replace(f'-{recycled_count - 1}', f'-{recycled_count}')
        else:
            value = f"{value}-{recycled_count}"
        return generate_slug_for_model(model, value, recycled_count)

    return slug[:50]

def absolute_url(req, url):
    """
        can either be a file instance or a URL string
    """
    try:
        return req.build_absolute_uri(url.url if hasattr(url, 'url') else url)
    except Exception:
        return None

def sizeof_fmt(num, suffix="B"):
    for unit in ["", "K", "M", "G"]:
        if abs(num) < 1024.0:
            return f"{num:3.1f} {unit}{suffix}"
        num /= 1024.0
    return f"{num:.1f} Yi{suffix}"


def file_location(path):
    from django.conf import settings
    import os

    if path.startswith('http'):
        path = exclude_host_from_url(path)

    if path.startswith("/static"):
        path = path[7:]

    if path.startswith('/'):
        path = path[1:]

    return os.path.join(settings.STATIC_ROOT, path)


def guess_file_type(filename):
    try:
        mimetype = mimetypes.guess_type(filename)[0].split('/')[0]
        return mimetype

    except Exception:
        return False

class FileFieldSerializer(serializers.CharField):
    """
        a field to handle uploaded file
    """

    def get_rpath(self, p):
        # extract relative path of doc
        return p[p.find('/static/') + 7:]

    def to_representation(self, value):
        request = self.context.get('request', None)
        if value:
            if isinstance(value, str):
                # If value is a string, assume it's a file path
                return value
            elif hasattr(value, 'url'):
                if 'http://' in str(value) or 'https://' in str(value):
                    return str(value)

                return absolute_url(request, value.url) if request else value.url
        return None

    def to_internal_value(self, data):
        if not data:
            return None

        if "/tmp/" not in data:
            # value not changed and here we simply return old file path
            return self.get_rpath(data)

        if data.startswith('http'):
            data = self.get_rpath(data)
            
        fpath = file_location(data)
        if not os.path.exists(fpath):
            raise serializers.ValidationError(f"File: '{fpath}' Does not exist")

        return File(open(fpath, 'rb'), os.path.basename(data))


class UploadTmpSerializer(serializers.Serializer):
    file = serializers.FileField()
    url = serializers.URLField(read_only=True)
    name = serializers.CharField(read_only=True)
    size = serializers.CharField(read_only=True)
    mime_type = serializers.CharField(read_only=True)

    def to_representation(self, instance):
        data = super(UploadTmpSerializer, self).to_representation(instance)
        data['file'] = instance['file']
        return data

    def store_file(self, file):
        from django.conf import settings
        static_path = settings.STATIC_ROOT

        os.makedirs(f'{static_path}/tmp', exist_ok=True)
        fpath = f"/tmp/{secrets.token_urlsafe(4)}-{file.name}"
        shutil.move(file.temporary_file_path(), static_path + fpath)
        os.chmod(static_path + fpath, 0o644)

        return {
            'file': fpath,
            'url': absolute_url(self.context['request'], f"/static{fpath}"),
            'name': file.name,
            'size': sizeof_fmt(file.size),
            'mime_type': guess_file_type(fpath)
        }

    def validate(self, attrs):
        file_details = self.store_file(attrs['file'])
        return file_details


class UploadTmpMedia(GenericAPIView):
    """
        Files will remove every 1 hour
    """
    parser_classes = (FormParser, MultiPartParser)
    serializer_class = UploadTmpSerializer

    def post(self, request: HttpRequest, *args, **kwargs):
        serializer = UploadTmpSerializer(data=request.FILES, context={'request': request})
        is_valid = serializer.is_valid(raise_exception=True)
        if not is_valid:
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        return Response(serializer.data)

# Configure filer admin after Django is fully loaded
def configure_filer_admin():
    try:
        from filer.admin.fileadmin import FileAdmin
        from filer.apps import FilerConfig

        FileAdmin.readonly_fields += ('owner',)
        FilerConfig.icon = 'icon-folder'
    except ImportError:
        pass

# This will be executed when this module is imported after Django is fully loaded

# Import date utility functions for easy access
try:
    from .date_utils import gregorian_to_jalali, jalali_to_gregorian, format_jalali_date
except ImportError:
    # Handle case where jdatetime is not installed
    def gregorian_to_jalali(*args, **kwargs):
        raise ImportError("jdatetime library is not installed. Run 'pip install jdatetime'")
    
    def jalali_to_gregorian(*args, **kwargs):
        raise ImportError("jdatetime library is not installed. Run 'pip install jdatetime'")
    
    def format_jalali_date(*args, **kwargs):
        raise ImportError("jdatetime library is not installed. Run 'pip install jdatetime'")


def create_copy_field_with_button(value, field_id, label="کپی"):
    """
    Create a copy field with button for Django admin
    Uses UNFOLD theme colors for consistent light/dark theme styling
    """
    from django.utils.html import format_html
    
    if not value:
        return '-'
    
    return format_html('''
        <style>
        /* Light theme styles (default) */
        .copy-field-container .copy-input {{
            flex: 1; 
            padding: 8px 12px; 
            border: 1px solid rgb(var(--color-base-300)); 
            border-radius: 6px; 
            background-color: rgb(var(--color-base-50));
            color: rgb(var(--color-base-800));
            font-size: 13px;
            font-family: system-ui, -apple-system, sans-serif;
        }}
        
        /* Dark theme styles */
        .dark .copy-field-container .copy-input,
        [data-theme="dark"] .copy-field-container .copy-input,
        html[data-color-scheme="dark"] .copy-field-container .copy-input {{
            background-color: rgb(51, 61, 77);
            color: rgb(255, 255, 255);
            border-color: rgb(75, 85, 99);
        }}
        
        .copy-field-container .copy-button {{
            padding: 8px; 
            background-color: rgb(var(--color-primary-500)); 
            color: white; 
            border: none; 
            border-radius: 6px; 
            cursor: pointer; 
            display: flex; 
            align-items: center; 
            justify-content: center;
            transition: all 0.2s ease;
            min-width: 32px;
            height: 32px;
        }}
        
        .copy-field-container .copy-button:hover {{
            background-color: rgb(var(--color-primary-600));
        }}
        
        .copy-field-container .copy-button.success {{
            background-color: rgb(34 197 94);
        }}
        
        .copy-field-container .copy-button.success:hover {{
            background-color: rgb(21 128 61);
        }}
        </style>
        
        <div class="copy-field-container" style="display: flex; align-items: center; gap: 8px; width: 100%;">
            <input type="text" value="{}" readonly 
                   class="copy-input"
                   id="copy_field_{}" />
            <button type="button" 
                    class="copy-button"
                    onclick="copyFieldToClipboard('copy_field_{}')" 
                    title="کپی {}">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z"/>
                </svg>
            </button>
        </div>
        
        <script>
            function copyFieldToClipboard(elementId) {{
                var copyText = document.getElementById(elementId);
                if (!copyText) return;
                
                copyText.select();
                copyText.setSelectionRange(0, 99999);
                
                try {{
                    navigator.clipboard.writeText(copyText.value).then(function() {{
                        showCopySuccess(copyText);
                    }}).catch(function() {{
                        // Fallback for older browsers
                        document.execCommand('copy');
                        showCopySuccess(copyText);
                    }});
                }} catch (err) {{
                    // Final fallback
                    document.execCommand('copy');
                    showCopySuccess(copyText);
                }}
            }}
            
            function showCopySuccess(copyText) {{
                var button = copyText.nextElementSibling;
                if (!button) return;
                
                var originalText = button.innerHTML;
                var originalClasses = button.className;
                
                button.innerHTML = '<svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor"><path d="M9 16.2L4.8 12l-1.4 1.4L9 19 21 7l-1.4-1.4L9 16.2z"/></svg>';
                button.className = originalClasses + ' success';
                
                setTimeout(function() {{
                    button.innerHTML = originalText;
                    button.className = originalClasses;
                }}, 2000);
            }}
        </script>
    ''', value, field_id, field_id, label)


def create_deposit_view_field_with_button(deposit_name, deposit_id, deposit_type="polldeposit", label="مشاهده"):
    """
    Create a deposit view field with eye button for Django admin
    Uses UNFOLD theme colors for consistent light/dark theme styling
    
    Args:
        deposit_name: Name of the deposit to display
        deposit_id: ID of the deposit
        deposit_type: Type of deposit (polldeposit, reportingdeposit, savingdeposit)
        label: Button label text
    """
    from django.utils.html import format_html
    from django.urls import reverse
    
    if not deposit_name or not deposit_id:
        return '-'
    
    # Map deposit types to admin URLs
    deposit_url_mapping = {
        'polldeposit': 'admin:deposit_polldeposit_change',
        'reportingdeposit': 'admin:deposit_reportingdeposit_change', 
        'savingdeposit': 'admin:deposit_savingdeposit_change',
    }
    
    # Generate the admin URL based on deposit type
    try:
        admin_url_name = deposit_url_mapping.get(deposit_type, 'admin:deposit_polldeposit_change')
        view_url = reverse(admin_url_name, args=[deposit_id])
    except:
        # Fallback if reverse fails
        view_url = f'/admin/deposit/{deposit_type}/{deposit_id}/change/'
    
    # Add preview query parameter
    view_url += '?preview=true'
    
    return format_html('''
        <div class="deposit-view-container" style="display: flex; align-items: center; gap: 8px; width: 100%;">
            <input type="text" value="{}" readonly 
                   class="deposit-input"
                   style="flex: 1; padding: 8px 12px; border: none; outline: none;
                          border-radius: 6px; font-size: 15px; font-weight: 500;
                          font-family: system-ui, -apple-system, sans-serif; 
                          background: transparent;" />
            <a href="{}" target="_blank" 
               class="deposit-view-button"
               style="padding: 8px; background-color: rgb(0 204 153); 
                      color: white; border: none; border-radius: 6px; cursor: pointer; 
                      display: flex; align-items: center; justify-content: center;
                      transition: all 0.2s ease; min-width: 32px; height: 32px;
                      text-decoration: none;"
               title="{}">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5zM12 17c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5zm0-8c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3z"/>
                </svg>
            </a>
        </div>
        
        <style>
        .deposit-view-container .deposit-input:focus {{
            outline: none !important;
            border: none !important;
            box-shadow: none !important;
        }}
        
        .deposit-view-container .deposit-view-button:hover {{
            background-color: rgb(0 184 138);
        }}
        </style>
    ''', deposit_name, view_url, label)


def badge_callback_unread_count_message(request):
    """
    Callback function برای نمایش تعداد پیام‌های خوانده نشده تیکت‌های reporting
    استفاده از منطق _get_messages_to_mark_as_read از admin_views.py
    
    Args:
        request: HttpRequest object
        
    Returns:
        int: تعداد پیام‌های خوانده نشده - فقط عدد
    """
    try:
        from apps.ticket.models import Ticket, TicketMessage
        from apps.deposit.models import DepositMembership
        from django.db.models import Q
        
        # بررسی اینکه کاربر لاگین باشد
        if not request.user.is_authenticated:
            return 0
        
        # دریافت تمام تیکت‌های reporting که کاربر باید ببینه
        report_tickets = Ticket.objects.filter(
            ticket_type=Ticket.TicketType.REPORT
        ).filter(
            Q(user=request.user) |  # تیکت‌هایی که خودش ایجاد کرده
            Q(deposit__members__user=request.user, 
              deposit__members__role__in=[DepositMembership.Role.OWNER, DepositMembership.Role.ADMIN],
              deposit__members__is_active=True)  # تیکت‌هایی که مدیر صندوقشونه
        ).distinct()
        
        unread_count = 0
        
        # برای هر تیکت، پیام‌های خوانده نشده مربوط به کاربر فعلی را حساب کن
        for ticket in report_tickets:
            # دریافت پیام‌های خوانده نشده این تیکت
            ticket_messages = TicketMessage.objects.filter(
                ticket=ticket,
                is_read=False
            ).exclude(user=request.user)  # پیام‌های خود کاربر را حذف کن
            
            # منطق تشخیص پیام‌های مربوط به کاربر فعلی (مشابه admin_views.py)
            messages_for_current_user = None
            
            # برای تیکت‌های گزارش
            if ticket.ticket_type == 'report':
                if request.user.is_superuser or request.user.is_staff:
                    # اگر کاربر فعلی پشتیبانی است، پیام‌های کاربران عادی را ببیند
                    messages_for_current_user = ticket_messages.exclude(
                        user__is_superuser=True
                    ).exclude(user__is_staff=True)
                else:
                    # اگر کاربر عادی است، پیام‌های پشتیبانی را ببیند
                    messages_for_current_user = ticket_messages.filter(
                        Q(user__is_superuser=True) | Q(user__is_staff=True)
                    )
            
            if messages_for_current_user is not None:
                unread_count += messages_for_current_user.count()
                
        return unread_count
        
    except Exception as e:
        print(f'-error-badge--> {e}')
        # در صورت بروز خطا، مقدار پیش‌فرض برگردان
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"Error in badge_callback_unread_count_message: {str(e)}")
        return 0
