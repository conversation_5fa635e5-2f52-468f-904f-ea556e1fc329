#!/usr/bin/env python
"""
Test script for Region Admin Dynamic Form functionality
"""

import os
import sys
import django
from django.test import TestCase, Client
from django.contrib.auth import get_user_model
from django.urls import reverse
from django.contrib.admin.sites import site

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.develop')
django.setup()

from apps.region.models import Region
from apps.region.admin import RegionAdminForm
from apps.account.models import User

def test_region_admin_form():
    """Test the RegionAdminForm functionality"""
    print("Testing RegionAdminForm...")

    import time
    timestamp = str(int(time.time()))

    # Clean up any existing test users
    User.objects.filter(email__startswith='form_test_').delete()

    # Create a test user
    user = User.objects.create_user(
        email=f'form_test_{timestamp}@example.com',
        fullname='Test User',
        phone_number=f'+**********{timestamp[-2:]}',
        password='testpass123'
    )
    
    # Test form initialization without instance
    form = RegionAdminForm()
    print("✓ Form initialized successfully")
    
    # Check if dynamic fields are present
    assert 'owner_email' in form.fields, "owner_email field missing"
    assert 'owner_password_display' in form.fields, "owner_password_display field missing"
    print("✓ Dynamic fields present in form")
    
    # Test form with data
    form_data = {
        'name': 'Test Region',
        'description': 'Test Description',
        'owner': user.pk,
        'owner_email': '<EMAIL>'
    }
    
    form = RegionAdminForm(data=form_data)
    if form.is_valid():
        print("✓ Form validation passed")
    else:
        print("✗ Form validation failed:", form.errors)
        return False
    
    # Create region and test form with instance
    region = Region.objects.create(
        name='Test Region',
        description='Test Description',
        owner=user
    )
    
    form = RegionAdminForm(instance=region)
    print("✓ Form initialized with instance")
    
    # Check if email field is populated
    if form.fields['owner_email'].initial == user.email:
        print("✓ Email field populated correctly")
    else:
        print("✗ Email field not populated correctly")
        return False
    
    # Test email update
    form_data['owner_email'] = '<EMAIL>'
    form = RegionAdminForm(data=form_data, instance=region)
    
    if form.is_valid():
        saved_region = form.save()
        user.refresh_from_db()
        if user.email == '<EMAIL>':
            print("✓ Email update functionality works")
        else:
            print("✗ Email update failed")
            return False
    else:
        print("✗ Form with updated email failed validation:", form.errors)
        return False
    
    # Cleanup
    region.delete()
    user.delete()
    
    print("✓ All RegionAdminForm tests passed!")
    return True

def test_ajax_endpoint():
    """Test the AJAX endpoint for user data"""
    print("\nTesting AJAX endpoint...")

    import time
    timestamp = str(int(time.time()))

    # Clean up any existing test users
    User.objects.filter(email__startswith='ajax_test_').delete()
    User.objects.filter(email__startswith='staff_test_').delete()

    # Create a test user
    user = User.objects.create_user(
        email=f'ajax_test_{timestamp}@example.com',
        fullname='AJAX Test User',
        phone_number=f'+9891234567{timestamp[-2:]}',
        password='testpass123'
    )

    # Create a staff user for testing
    staff_user = User.objects.create_user(
        email=f'staff_test_{timestamp}@example.com',
        fullname='Staff User',
        phone_number=f'+9891234568{timestamp[-2:]}',
        password='staffpass123'
    )
    staff_user.is_staff = True
    staff_user.save()
    
    client = Client()
    client.force_login(staff_user)
    
    # Test the AJAX endpoint
    response = client.get('/api/region/admin/get-user-data/', {'user_id': user.pk})
    
    if response.status_code == 200:
        data = response.json()
        if data.get('email') == user.email:
            print("✓ AJAX endpoint returns correct user data")
        else:
            print("✗ AJAX endpoint returned incorrect data:", data)
            return False
    else:
        print("✗ AJAX endpoint failed with status:", response.status_code)
        return False
    
    # Test with invalid user ID
    response = client.get('/api/region/admin/get-user-data/', {'user_id': 99999})
    if response.status_code == 404:
        print("✓ AJAX endpoint handles invalid user ID correctly")
    else:
        print("✗ AJAX endpoint should return 404 for invalid user ID")
        return False
    
    # Cleanup
    user.delete()
    staff_user.delete()
    
    print("✓ All AJAX endpoint tests passed!")
    return True

def test_static_files():
    """Test if static files exist"""
    print("\nTesting static files...")
    
    js_file = 'static/js/region_admin_dynamic.js'
    css_file = 'static/css/region_admin_dynamic.css'
    
    if os.path.exists(js_file):
        print("✓ JavaScript file exists")
    else:
        print("✗ JavaScript file missing:", js_file)
        return False
    
    if os.path.exists(css_file):
        print("✓ CSS file exists")
    else:
        print("✗ CSS file missing:", css_file)
        return False
    
    # Check if files have content
    with open(js_file, 'r') as f:
        js_content = f.read()
        if 'initializeDynamicForm' in js_content:
            print("✓ JavaScript file has expected content")
        else:
            print("✗ JavaScript file missing expected content")
            return False
    
    with open(css_file, 'r') as f:
        css_content = f.read()
        if 'dynamic-owner-fields' in css_content:
            print("✓ CSS file has expected content")
        else:
            print("✗ CSS file missing expected content")
            return False
    
    print("✓ All static file tests passed!")
    return True

def main():
    """Run all tests"""
    print("Starting Region Admin Dynamic Form Tests...\n")
    
    tests = [
        test_static_files,
        test_region_admin_form,
        test_ajax_endpoint,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                print(f"✗ {test.__name__} failed")
        except Exception as e:
            print(f"✗ {test.__name__} failed with exception: {e}")
    
    print(f"\n{'='*50}")
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Implementation is ready.")
        return True
    else:
        print("❌ Some tests failed. Please review the implementation.")
        return False

if __name__ == '__main__':
    main()
