{% extends "admin/base_site.html" %}
{% load i18n static admin_urls unfold humanize %}

{% block extrahead %}
    {{ block.super }}
    <script src="{% url 'admin:jsi18n' %}"></script>
    <link href="https://cdn.jsdelivr.net/gh/rastikerdar/vazirmatn@v33.003/Vazirmatn-font-face.css" rel="stylesheet" type="text/css" />
    <style>
      /* Font family override */
      body, .unfold {
        font-family: 'Vazirmatn', system-ui, sans-serif !important;
      }
      
      /* Custom styles for tab system */
      .tab-content {
        display: none;
      }
      
      .tab-content.active {
        display: block;
      }
      
      /* Custom row styles using Unfold color variables */
      .owner-row {
        background-color: rgba(34, 197, 94, 0.1) !important;
        border: 1px solid rgba(34, 197, 94, 0.3) !important;
      }
      
      .dark .owner-row {
        background-color: rgba(34, 197, 94, 0.2) !important;
        border: 1px solid rgba(34, 197, 94, 0.4) !important;
      }
      
      .owner-row td:first-child {
        border-left: 3px solid rgb(34, 197, 94) !important;
      }
      
      .completed-row {
        background-color: rgba(34, 197, 94, 0.1) !important;
      }
      
      .pending-row {
        background-color: rgba(245, 158, 11, 0.1) !important;
      }
      
      .overdue-row {
        background-color: rgba(239, 68, 68, 0.1) !important;
      }
      
      .dark .completed-row {
        background-color: rgba(34, 197, 94, 0.2) !important;
      }
      
      .dark .pending-row {
        background-color: rgba(245, 158, 11, 0.2) !important;
      }
      
      .dark .overdue-row {
        background-color: rgba(239, 68, 68, 0.2) !important;
      }
      
      .completed-row td:first-child {
        border-left: 3px solid rgb(34, 197, 94) !important;
      }
      
      .pending-row td:first-child {
        border-left: 3px solid rgb(245, 158, 11) !important;
      }
      
      .overdue-row td:first-child {
        border-left: 3px solid rgb(239, 68, 68) !important;
      }
    </style>
    <script>
      document.addEventListener('DOMContentLoaded', function() {
        // Tab functionality
        const tabButtons = document.querySelectorAll('[data-tab]');
        const tabContents = document.querySelectorAll('.tab-content');
        
        tabButtons.forEach(button => {
          button.addEventListener('click', () => {
            // Remove active class from all buttons
            tabButtons.forEach(btn => {
              btn.classList.remove('border-primary-500', 'font-semibold', 'text-primary-500');
              btn.classList.add('border-transparent', 'text-gray-500');
            });
            
            // Hide all tab contents
            tabContents.forEach(content => {
              content.classList.remove('active');
            });
            
            // Add active class to clicked button
            button.classList.remove('border-transparent', 'text-gray-500');
            button.classList.add('border-primary-500', 'font-semibold', 'text-primary-500');
            
            // Show corresponding tab content
            const tabId = button.getAttribute('data-tab');
            document.getElementById(tabId).classList.add('active');
          });
        });
      });
    </script>
{% endblock %}

{% block breadcrumbs %}{% if not is_popup %}
    <div class="px-4 lg:px-8">
        <div class="container mb-6 mx-auto -my-3 lg:mb-12">
            <ul class="flex flex-wrap">
                {% url 'admin:index' as link %}
                {% trans 'Home' as name %}
                {% include 'unfold/helpers/breadcrumb_item.html' with link=link name=name %}

                {% url 'admin:deposit_savingdeposit_changelist' as link %}
                {% trans 'Saving Deposit' as name %}
                {% include 'unfold/helpers/breadcrumb_item.html' with link=link name=name %}

                {% url 'admin:deposit_savingdeposit_detail' object_id=deposit_id as link %}
                {% trans 'Saving Deposit Detail' as name %} 
                {% include 'unfold/helpers/breadcrumb_item.html' with link=link name=name %}
            </ul>
        </div>
    </div>
{% endif %}{% endblock %}

{% block content %}
    {% component "unfold/components/container.html" %}
        <!-- Main Deposit Information Card -->
        {% component "unfold/components/card.html" with class="mb-6" %}
            <!-- Header with Edit button on the right -->
            <div class="flex justify-between items-center mb-6 p-6">
                <div class="flex items-center">
                    <img src="{% static 'images/deposit-saving.svg' %}" alt="Saving Deposit" class="w-12 h-12 mr-3">
                    <h2 class="text-2xl font-semibold">
                        {{ deposit_title }}
                    </h2>
                </div>
            {% if not is_preview %}

                <div class="flex space-x-2">
                    {% component "unfold/components/button.html" with href=change_form_url submit_text=_("View/Edit") %}{% endcomponent %}
                </div>
            {% endif %}
            </div>
            
            <div class="p-6 pt-0">                
                <!-- Grid layout using Tailwind -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Deposit Balance -->
                    <div class="flex justify-between items-center">
                        <div class="flex items-center">
                            <span class="icon icon-account_balance_wallet mr-2 text-primary-500"></span>
                            <span class="text-gray-700 dark:text-gray-300">{% trans "Deposit Balance:" %}</span>
                        </div>
                        <span class="font-semibold">
                            {{ deposit_balance|intcomma }}
                        </span>
                    </div>
                    
                    <!-- Amount per Share -->
                    <div class="flex justify-between items-center">
                        <div class="flex items-center">
                            <span class="icon icon-attach_money mr-2 text-primary-500"></span>
                            <span class="text-gray-700 dark:text-gray-300">{% trans "Amount per Share:" %}</span>
                        </div>
                        <span class="font-semibold">
                            {{ amount_per_share|intcomma }}
                        </span>
                    </div>
                    
                    <!-- Payment Cycle -->
                    <div class="flex justify-between items-center">
                        <div class="flex items-center">
                            <span class="icon icon-calendar_month mr-2 text-primary-500"></span>
                            <span class="text-gray-700 dark:text-gray-300">{% trans "Payment Cycle:" %}</span>
                        </div>
                        <span class="font-semibold">
                            {{ payment_cycle }}
                        </span>
                    </div>
                    
                    <!-- Validity Duration -->
                    <div class="flex justify-between items-center">
                        <div class="flex items-center">
                            <span class="icon icon-schedule mr-2 text-primary-500"></span>
                            <span class="text-gray-700 dark:text-gray-300">{% trans "Validity Duration:" %}</span>
                        </div>
                        <span class="font-semibold">
                            {{ validity_duration }}
                        </span>
                    </div>

                    <!-- Due Date -->
                    <div class="flex justify-between items-center">
                        <div class="flex items-center">
                            <span class="icon icon-event_upcoming mr-2 text-primary-500"></span>
                            <span class="text-gray-700 dark:text-gray-300">{% trans "Due Date:" %}</span>
                        </div>
                        <span class="font-semibold">{{ due_date }}</span>
                    </div>
                    
                    <!-- Region Name -->
                    <div class="flex justify-between items-center">
                        <div class="flex items-center">
                            <span class="icon icon-location_on mr-2 text-primary-500"></span>
                            <span class="text-gray-700 dark:text-gray-300">{% trans "Region Name:" %}</span>
                        </div>
                        <span class="font-semibold">
                            {{ regin_name }}
                        </span>
                    </div>
                </div>
            </div>
        {% endcomponent %}
        
        <!-- Action Buttons -->
        {% if not is_preview %}
        <div class="grid grid-cols-2 md:grid-cols-3 gap-4 mb-6">
            {% component "unfold/components/button.html" with href=payment_url submit_text=_("Payments") %}{% endcomponent %}
            
            {% component "unfold/components/button.html" with href=transaction_url submit_text=_("Transactions") variant="danger" %}{% endcomponent %}
            
            {% component "unfold/components/button.html" with href=request_join_url submit_text=_("Membership Requests") variant="secondary" %}{% endcomponent %}
            
            {% component "unfold/components/button.html" with href=request_loan_url submit_text=_("Loans") variant="secondary" %}{% endcomponent %}
            
            {% component "unfold/components/button.html" with href=tickets_url submit_text=_("Tickets") variant="secondary" %}{% endcomponent %}
            
            {% component "unfold/components/button.html" with href=medias_url submit_text=_("Media") variant="secondary" %}{% endcomponent %}
        </div>
        {% endif %}
        
        <!-- Tab System -->
        {% component "unfold/components/card.html" %}
            <!-- Custom tab navigation -->
            <div class="border-b border-gray-200">
                <div class="flex space-x-4 px-6">
                    <button class="py-3 px-4 border-b-2 border-primary-500 font-semibold text-primary-500" data-tab="members-tab">{% trans "Members" %}</button>
                    <button class="py-3 px-4 border-b-2 border-transparent text-gray-500 hover:text-gray-700" data-tab="duedate-tab">{% trans "DueDates" %}</button>
                    <button class="py-3 px-4 border-b-2 border-transparent text-gray-500 hover:text-gray-700" data-tab="loans-tab">{% trans "Loans" %}</button>
                </div>
            </div>
            
            <!-- Tab contents -->
            <div class="p-6">
                <!-- Members Tab -->
                <div id="members-tab" class="tab-content active">
                    {% if deposit_members %}
                        {% component "unfold/components/table.html" with table=members_table_data striped=True %}{% endcomponent %}
                    {% else %}
                        <div class="text-center py-8">
                            <img src="{% static 'images/image_null_deposit.svg' %}" alt="No data" class="mx-auto h-32">
                            <p class="mt-4 text-gray-500">
                                {% trans "No data" %}
                            </p>
                        </div>
                    {% endif %}
                </div>
                
                <!-- Due Dates Tab -->
                <div id="duedate-tab" class="tab-content">
                    {% if due_dates_table_data.rows %}
                        {% component "unfold/components/table.html" with table=due_dates_table_data striped=True %}{% endcomponent %}
                    {% else %}
                        <div class="text-center py-8">
                            <img src="{% static 'images/image_null_deposit.svg' %}" alt="No data" class="mx-auto h-32">
                            <p class="mt-4 text-gray-500">
                                {% trans "No due dates available" %}
                            </p>
                        </div>
                    {% endif %}
                </div>
                
                <!-- Loans Tab -->
                <div id="loans-tab" class="tab-content">
                    {% if loan_members_table_data.rows %}
                        {% component "unfold/components/table.html" with table=loan_members_table_data striped=True %}{% endcomponent %}
                    {% else %}
                        <div class="text-center py-8">
                            <img src="{% static 'images/image_null_deposit.svg' %}" alt="No data" class="mx-auto h-32">
                            <p class="mt-4 text-gray-500">
                                {% trans "No loans available" %}
                            </p>
                        </div>
                    {% endif %}
                </div>
            </div>
        {% endcomponent %}
    {% endcomponent %}
{% endblock %}