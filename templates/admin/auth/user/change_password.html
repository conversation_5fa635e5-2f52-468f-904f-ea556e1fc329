{% extends "admin/base_site.html" %}
{% load admin_urls i18n static unfold %}

{% block branding %}
    {% include "unfold/helpers/site_branding.html" %}
{% endblock %}

{% block extrahead %}
    {{ block.super }}
    <script src="{% url 'admin:jsi18n' %}"></script>
    <link href="https://cdn.jsdelivr.net/gh/rastikerdar/vazirmatn@v33.003/Vazirmatn-font-face.css" rel="stylesheet" type="text/css" />
    <!-- Material Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <style>
      :root {
        font-family: 'Vazirmatn', system-ui, sans-serif;
      }
      
      /* Material Icons styling */
      .material-icons {
        font-size: 20px !important;
        vertical-align: middle;
        margin-right: 8px;
      }
      
      /* Password form styling */
      .password-form-container {
        min-height: 400px;
      }
      
      /* Enhanced security visual */
      .security-icon {
        font-size: 48px !important;
        color: var(--color-primary-500);
      }
      
      /* Hide duplicate labels in password fields */
      .password-field label {
        display: none !important;
      }
    </style>
{% endblock %}

{% block bodyclass %}{{ block.super }} {{ opts.app_label }}-{{ opts.model_name }} change-form{% endblock %}

{% block breadcrumbs %}
{% if not is_popup %}
    <div class="px-4 lg:px-8">
        <div class="container mb-6 mx-auto -my-3 lg:mb-12">
            <ul class="flex flex-wrap">
                {% url 'admin:index' as link %}
                {% trans 'Home' as name %}
                {% include 'unfold/helpers/breadcrumb_item.html' with link=link name=name %}

                {% url 'admin:app_list' app_label=opts.app_label as link %}
                {% include 'unfold/helpers/breadcrumb_item.html' with link=link name=opts.app_config.verbose_name %}

                {% url opts|admin_urlname:'changelist' as link %}
                {% include 'unfold/helpers/breadcrumb_item.html' with link=link name=opts.verbose_name_plural|capfirst %}

                {% url opts|admin_urlname:'change' original.pk|admin_urlquote as link %}
                {% include 'unfold/helpers/breadcrumb_item.html' with link=link name=original|truncatewords:"18" %}

                {% translate 'Change password' as breadcrumb_name %}
                {% include 'unfold/helpers/breadcrumb_item.html' with link='' name=breadcrumb_name %}
            </ul>
        </div>
    </div>
{% endif %}
{% endblock %}

{% block content %}
    {% component "unfold/components/container.html" %}
        <!-- Main Password Change Card -->
        {% component "unfold/components/card.html" with class="mb-6 password-form-container" %}
            <!-- Header with Security Icon -->
            <div class="flex justify-between items-center mb-8">
                <div class="flex items-center">
                    <span class="material-icons security-icon mr-4">security</span>
                    {% component "unfold/components/title.html" with class="text-2xl" %}
                        {% translate "Change Password" %}
                    {% endcomponent %}
                </div>
                <div class="flex items-center">
                    <span class="material-icons text-gray-500">person</span>
                    {% component "unfold/components/text.html" with class="text-gray-600 font-medium" %}
                            {% blocktranslate with username=original %}
                            <strong>{{ username }}</strong>
                            {% endblocktranslate %}
                    {% endcomponent %}
                </div>
            </div>


            <!-- Error Messages -->
            {% if form.errors %}
                {% component "unfold/components/card.html" with class="mb-6 bg-red-50 border-l-4 border-red-400 dark:bg-red-900/20" %}
                    <div class="flex">
                        <span class="material-icons text-red-500 mt-1">error</span>
                        <div class="ml-3">
                            {% for field, errors in form.errors.items %}
                                {% for error in errors %}
                                    {% component "unfold/components/text.html" with class="text-red-700 dark:text-red-300 block" %}
                                        {{ error }}
                                    {% endcomponent %}
                                {% endfor %}
                            {% endfor %}
                        </div>
                    </div>
                {% endcomponent %}
            {% endif %}

            <!-- Password Form -->
            <form{% if form_url %} action="{{ form_url }}"{% endif %} method="post" id="{{ opts.model_name }}_form">
                {% csrf_token %}{% block form_top %}{% endblock %}
                <input type="text" name="username" value="{{ original.get_username }}" class="hidden">

                {% if is_popup %}
                    <input type="hidden" name="{{ is_popup_var }}" value="1">
                {% endif %}

                <!-- Password Fields Card -->
                {% component "unfold/components/card.html" with class="mb-8 bg-gray-50 dark:bg-gray-800/50" %}
                    <div class="space-y-6">
                        <!-- New Password Field -->
                        <div class="space-y-2 password-field">
                            {% component "unfold/components/flex.html" with class="items-center mb-2" %}
                                <span class="material-icons text-primary-500">lock</span>
                                {% component "unfold/components/text.html" with class="font-medium" %}
                                    {{ form.password1.label }}
                                {% endcomponent %}
                            {% endcomponent %}
                            {% include "unfold/helpers/field.html" with field=form.password1 %}
                        </div>

                        <!-- Confirm Password Field -->
                        <div class="space-y-2 password-field">
                            {% component "unfold/components/flex.html" with class="items-center mb-2" %}
                                <span class="material-icons text-primary-500">lock_outline</span>
                                {% component "unfold/components/text.html" with class="font-medium" %}
                                    {{ form.password2.label }}
                                {% endcomponent %}
                            {% endcomponent %}
                            {% include "unfold/helpers/field.html" with field=form.password2 %}
                        </div>
                    </div>
                {% endcomponent %}

                <!-- Action Buttons -->
                <div class="flex justify-between items-center">
                    <!-- Cancel Button -->
                    {% url opts|admin_urlname:'change' original.pk|admin_urlquote as cancel_url %}
                    {% component "unfold/components/button.html" with href=cancel_url variant="secondary" %}
                        <span class="material-icons">cancel</span>
                        {% translate "Cancel" %}
                    {% endcomponent %}

                    <!-- Submit Button -->
                    {% component "unfold/components/button.html" with type="submit" class="ml-auto" %}
                        <span class="material-icons">save</span>
                        {% translate "Change password" %}
                    {% endcomponent %}
                </div>
            </form>

            <!-- Security Tips -->
            {% component "unfold/components/card.html" with class="mt-8 bg-green-50 border-l-4 border-green-400 dark:bg-green-900/20" %}
                <div class="flex">
                    <span class="material-icons text-green-500 mt-1">shield</span>
                    <div class="ml-3">
                        {% component "unfold/components/text.html" with class="text-green-700 dark:text-green-300 font-medium mb-2" %}
                            {% translate "Security Tips:" %}
                        {% endcomponent %}
                        <ul class="text-green-600 dark:text-green-400 text-sm space-y-1 list-disc list-inside">
                            <li>{% translate "Use a strong password with at least 8 characters" %}</li>
                            <li>{% translate "Include uppercase and lowercase letters, numbers, and symbols" %}</li>
                            <li>{% translate "Don't reuse passwords from other accounts" %}</li>
                            <li>{% translate "Consider using a password manager" %}</li>
                        </ul>
                    </div>
                </div>
            {% endcomponent %}

        {% endcomponent %}
    {% endcomponent %}
{% endblock %}