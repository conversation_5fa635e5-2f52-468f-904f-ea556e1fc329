{% load i18n %}
<div class="json-editor-container">
    <textarea style="display: none" name="{{ widget.name }}" id="{{ widget.attrs.id }}" {%
        include "django/forms/widgets/attrs.html" %}>{% if widget.value %}{{ widget.value }}{% endif %}</textarea>
    <div class="json-view-editor" id='date-view-editor-{{ widget.attrs.id }}'></div>
</div>

<script defer="defer">
    document.addEventListener('DOMContentLoaded', function () {
        function initJsonEditor() {
            let editor_ = document.getElementById("{{ widget.attrs.id }}");
            if (!editor_) {
                console.error("Editor element not found");
                return;
            }

            let startValue;
            try {
                startValue = editor_.value && editor_.value.trim() !== '' ? JSON.parse(editor_.value) : [];
            } catch (e) {
                console.error("Error parsing JSON value:", e);
                startValue = [];
            }

            let jsonViewerDiv = document.getElementById('date-view-editor-{{ widget.attrs.id }}');

            if (typeof JSONEditor === 'undefined') {
                console.error("JSONEditor is not defined. Make sure the library is loaded.");
                return;
            }

            // Custom template for add button
            JSONEditor.defaults.templates.button = function (text, icon, title) {
                let el = document.createElement('button');
                el.type = 'button';
                el.classList.add('json-editor-btn-modern');

                if (icon) {
                    let iconEl = document.createElement('span');
                    iconEl.classList.add('json-editor-btn-icon');
                    iconEl.innerHTML = icon;
                    el.appendChild(iconEl);
                }

                if (text) {
                    let textEl = document.createElement('span');
                    textEl.classList.add('json-editor-btn-text');
                    textEl.textContent = text;
                    el.appendChild(textEl);
                }

                if (title) el.title = title;

                return el;
            };

            // Custom icons
            JSONEditor.defaults.iconlib = {
                getIcon: function (key) {
                    switch (key) {
                        case 'add':
                            return '<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16"><path d="M8 4a.5.5 0 0 1 .5.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3A.5.5 0 0 1 8 4z"/></svg>';
                        case 'delete':
                            return '<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16"><path d="M5.5 5.5A.5.5 0 0 1 6 6v6a.5.5 0 0 1-1 0V6a.5.5 0 0 1 .5-.5zm2.5 0a.5.5 0 0 1 .5.5v6a.5.5 0 0 1-1 0V6a.5.5 0 0 1 .5-.5zm3 .5a.5.5 0 0 0-1 0v6a.5.5 0 0 0 1 0V6z"/><path fill-rule="evenodd" d="M14.5 3a1 1 0 0 1-1 1H13v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V4h-.5a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1H6a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1h3.5a1 1 0 0 1 1 1v1zM4.118 4 4 4.059V13a1 1 0 0 0 1 1h6a1 1 0 0 0 1-1V4.059L11.882 4H4.118zM2.5 3V2h11v1h-11z"/></svg>';
                        case 'edit':
                            return '<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16"><path d="M12.146.146a.5.5 0 0 1 .708 0l3 3a.5.5 0 0 1 0 .708l-10 10a.5.5 0 0 1-.168.11l-5 2a.5.5 0 0 1-.65-.65l2-5a.5.5 0 0 1 .11-.168l10-10zM11.207 2.5 13.5 4.793 14.793 3.5 12.5 1.207 11.207 2.5zm1.586 3L10.5 3.207 4 9.707V10h.5a.5.5 0 0 1 .5.5v.5h.5a.5.5 0 0 1 .5.5v.5h.293l6.5-6.5zm-9.761 5.175-.106.106-1.528 3.821 3.821-1.528.106-.106A.5.5 0 0 1 5 12.5V12h-.5a.5.5 0 0 1-.5-.5V11h-.5a.5.5 0 0 1-.468-.325z"/></svg>';
                        case 'moveup':
                            return '<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16"><path fill-rule="evenodd" d="M8 15a.5.5 0 0 0 .5-.5V2.707l3.146 3.147a.5.5 0 0 0 .708-.708l-4-4a.5.5 0 0 0-.708 0l-4 4a.5.5 0 1 0 .708.708L7.5 2.707V14.5a.5.5 0 0 0 .5.5z"/></svg>';
                        case 'movedown':
                            return '<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16"><path fill-rule="evenodd" d="M8 1a.5.5 0 0 1 .5.5v11.793l3.146-3.147a.5.5 0 0 1 .708.708l-4 4a.5.5 0 0 1-.708 0l-4-4a.5.5 0 0 1 .708-.708L7.5 13.293V1.5A.5.5 0 0 1 8 1z"/></svg>';
                        default:
                            return '';
                    }
                }
            };

            try {
                let jsonEditor = new JSONEditor(
                    jsonViewerDiv, {
                    theme: 'bootstrap4',
                    schema: {{ widget.attrs.schema | safe }},
            disable_edit_json: true,
                disable_properties: true,
                    disable_array_delete_all_rows: false,
                        disable_array_delete_last_row: true, // Disable delete last row button
                            disable_array_reorder: true, // Disable array reordering
                                grid_columns: 12,
                                    prompt_before_delete: true,
                                        disable_collapse: false, // Enable collapse to show button sections
                                            show_errors: 'always',
                                                startval: startValue,
                                                    iconlib: 'custom',
                                                        object_layout: 'normal', // Changed from grid to normal for better layout
                                                            enable_array_copy: false, // Disable copy functionality
                                                                show_opt_in: false,
                                                                    compact: false,
                                                                        array_controls_top: false, // Move array controls to bottom
                                                                            show_button_bar: true, // Show button bar
                                                                                form_name_root: 'root' // Add a root name for better structure
        }
                );

    // Store the editor instance on the textarea
    editor_.editor = jsonEditor;

    // Update the textarea when the editor changes
    jsonEditor.on('change', function () {
        editor_.value = JSON.stringify(jsonEditor.getValue());

        // Trigger a change event on the textarea
        let event = new Event('change', { bubbles: true });
        editor_.dispatchEvent(event);

        // Apply styling to newly added elements
        applyCustomStyling();
    });

    // Function to apply custom styling to all elements
    function applyCustomStyling() {
        // Add modern styling to buttons
        const allButtons = jsonViewerDiv.querySelectorAll('button');
        allButtons.forEach(button => {
            if (!button.classList.contains('json-editor-btn-modern')) {
                button.classList.add('json-editor-btn-modern');

                // Add specific styling based on button type
                if (button.classList.contains('json-editor-btntype-add')) {
                    button.classList.add('json-editor-btn-add');
                } else if (button.classList.contains('json-editor-btntype-delete') ||
                    button.classList.contains('json-editor-btntype-deleteall') ||
                    button.classList.contains('json-editor-btntype-deletelast')) {
                    button.classList.add('json-editor-btn-delete');
                }
            }
        });

        // Style form controls
        const formControls = jsonViewerDiv.querySelectorAll('input, select, textarea');
        formControls.forEach(control => {
            control.classList.add('modern-form-control');
        });

        // Style table headers
        const tableHeaders = jsonViewerDiv.querySelectorAll('th');
        tableHeaders.forEach(header => {
            header.classList.add('modern-table-header');
        });

        // Style table rows
        const tableRows = jsonViewerDiv.querySelectorAll('tr');
        tableRows.forEach(row => {
            row.classList.add('modern-table-row');
        });

        // Make table full width
        const tables = jsonViewerDiv.querySelectorAll('table');
        tables.forEach(table => {
            table.classList.add('full-width-table');
        });

        // Make table cells take equal space
        const tableCells = jsonViewerDiv.querySelectorAll('td');
        tableCells.forEach(cell => {
            if (!cell.classList.contains('table-controls-cell')) {
                cell.classList.add('equal-width-cell');
            }
        });

        // Add special styling to control cells
        const controlCells = jsonViewerDiv.querySelectorAll('td:last-child');
        controlCells.forEach(cell => {
            cell.classList.add('table-controls-cell');
        });

        // Fix button group styling
        const buttonGroups = jsonViewerDiv.querySelectorAll('.btn-group, .json-editor-btngroup');
        buttonGroups.forEach(group => {
            group.classList.add('modern-btn-group');
        });

        // Fix card styling
        const cards = jsonViewerDiv.querySelectorAll('.card');
        cards.forEach(card => {
            card.classList.add('modern-card');
        });

        // Ensure button sections are visible
        const buttonSections = jsonViewerDiv.querySelectorAll('.json-editor-btngroup');
        buttonSections.forEach(section => {
            section.style.display = 'flex';
            section.style.visibility = 'visible';
        });

        // Style button bars
        const buttonBars = jsonViewerDiv.querySelectorAll('.json-editor-btn-bar');
        buttonBars.forEach(bar => {
            bar.style.display = 'flex';
            bar.style.flexWrap = 'wrap';
            bar.style.gap = '0.5rem';
            bar.style.marginTop = '1rem';
            bar.style.marginBottom = '0';
            bar.style.padding = '0';
            bar.style.backgroundColor = 'transparent';
            bar.style.borderRadius = '0';
            bar.style.width = '100%';
            bar.style.justifyContent = 'flex-end';

            // Move button bar to the end of its parent container
            const parent = bar.parentElement;
            if (parent) {
                parent.style.display = 'flex';
                parent.style.flexDirection = 'column';
                parent.appendChild(bar);
            }
        });

        // Hide specific buttons (Copy, Move up, Move down, Delete Last)
        const buttonsToHide = jsonViewerDiv.querySelectorAll('.json-editor-btntype-copy, .json-editor-btntype-move, .json-editor-btntype-deletelast');
        buttonsToHide.forEach(button => {
            button.style.display = 'none';
        });

        // Ensure form fields take full width
        const formRows = jsonViewerDiv.querySelectorAll('.row');
        formRows.forEach(row => {
            row.style.width = '100%';
            const cols = row.querySelectorAll('[class*="col-"]');
            cols.forEach(col => {
                col.style.width = '100%';
                col.style.maxWidth = '100%';
                col.style.flex = '0 0 100%';
            });
        });
    }

    // Apply styling immediately after initialization
    setTimeout(applyCustomStyling, 100);

    // Process error messages to make them HTML5-like
    function processErrorMessages() {
        const errorElements = jsonViewerDiv.querySelectorAll('.je-error');
        errorElements.forEach(error => {
            // Get the error message text
            const errorText = error.textContent.trim();

            // Set the data-content attribute for the tooltip
            error.setAttribute('data-content', errorText);

            // Find the parent form group
            const formGroup = error.closest('.form-group');
            if (formGroup) {
                formGroup.classList.add('has-error');

                // Find the input element
                const input = formGroup.querySelector('input, select, textarea');
                if (input) {
                    // Add error class to the input
                    input.classList.add('is-invalid');

                    // Add title attribute for native tooltip
                    input.setAttribute('title', errorText);
                }
            }
        });
    }

    // Remove "Delete Last Course Features" button if it exists
    function removeDeleteLastButton() {
        const deleteLastButtons = jsonViewerDiv.querySelectorAll('button.json-editor-btntype-deletelast');
        deleteLastButtons.forEach(button => {
            const buttonText = button.textContent.trim();
            if (buttonText.includes('Delete Last') && buttonText.includes('Course Features')) {
                button.style.display = 'none';
            }
        });
    }

    // Add mutation observer to apply styling to dynamically added elements
    const observer = new MutationObserver(function (mutations) {
        applyCustomStyling();
        processErrorMessages();
        removeDeleteLastButton();
    });

    observer.observe(jsonViewerDiv, {
        childList: true,
        subtree: true
    });

    // Initial processing
    setTimeout(() => {
        processErrorMessages();
        removeDeleteLastButton();
    }, 200);

            } catch (e) {
        console.error("Error initializing JSONEditor:", e);
    }
        }

    // Initialize the editor
    if (document.readyState === 'complete') {
        initJsonEditor();
    } else {
        window.addEventListener('load', initJsonEditor);
    }
    });
</script>

<style>
    /*
    * JSON Editor Enhanced Styling - UNFOLD Admin Theme Integration
    *
    * This stylesheet integrates the JSON editor field with the UNFOLD admin theme
    * using the exact color palette and design patterns from the UNFOLD configuration.
    *
    * UNFOLD Color Palette (from config/settings/base.py):
    * ====================================================
    *
    * Primary Colors:
    * - primary-50: rgb(232, 255, 248)   #E8FFF8
    * - primary-100: rgb(204, 255, 240)  #CCFFF0
    * - primary-200: rgb(153, 247, 222)  #99F7DE
    * - primary-300: rgb(102, 235, 200)  #66EBC8
    * - primary-400: rgb(51, 220, 175)   #33DCAF
    * - primary-500: rgb(0, 204, 153)    #00CC99 - Main accent color
    * - primary-600: rgb(0, 184, 138)    #00B88A - Hover states
    * - primary-700: rgb(0, 153, 115)    #009973
    * - primary-800: rgb(0, 122, 92)     #007A5C
    * - primary-900: rgb(0, 92, 69)      #005C45
    * - primary-950: rgb(0, 61, 46)      #003D2E
    *
    * Secondary Colors:
    * - secondary-500: rgb(1, 53, 59)    #01353B - Text, delete buttons
    * - secondary-600: rgb(1, 43, 48)    #012B30 - Hover states
    * - secondary-700: rgb(1, 36, 40)    #012428
    * - secondary-800: rgb(1, 30, 34)    #011E22
    *
    * Dark Mode Colors:
    * - dark-50: rgb(18, 26, 25)         #121A19 - Deepest background
    * - dark-100: rgb(22, 32, 30)        #16201E - Main background
    * - dark-200: rgb(26, 38, 36)        #1A2624 - Card backgrounds
    * - dark-300: rgb(30, 44, 42)        #1E2C2A - Active elements
    * - dark-400: rgb(34, 50, 48)        #223230 - Borders
    * - dark-500: rgb(38, 56, 54)        #263836 - Separators
    *
    * Base Colors (Grays):
    * - base-50: rgb(249, 250, 251)      #F9FAFB
    * - base-100: rgb(243, 244, 246)     #F3F4F6
    * - base-200: rgb(229, 231, 235)     #E5E7EB
    * - base-300: rgb(209, 213, 219)     #D1D5DB
    * - base-400: rgb(156, 163, 175)     #9CA3AF
    * - base-500: rgb(107, 114, 128)     #6B7280
    * - base-600: rgb(75, 85, 99)        #4B5563
    * - base-700: rgb(55, 65, 81)        #374151
    * - base-800: rgb(31, 41, 55)        #1F2937
    * - base-900: rgb(17, 24, 39)        #111827
    */

    /* CSS Custom Properties for UNFOLD Professional Theme Integration */
    :root {
        /* Professional Color Palette - Using UNFOLD Base Colors */
        --json-editor-primary-50: rgb(249, 250, 251);
        /* base-50 - Lightest backgrounds */
        --json-editor-primary-100: rgb(243, 244, 246);
        /* base-100 - Light backgrounds */
        --json-editor-primary-200: rgb(229, 231, 235);
        /* base-200 - Borders, dividers */
        --json-editor-primary-300: rgb(209, 213, 219);
        /* base-300 - Subtle borders */
        --json-editor-primary-400: rgb(156, 163, 175);
        /* base-400 - Placeholders */
        --json-editor-primary-500: rgb(107, 114, 128);
        /* base-500 - Main interactive elements */
        --json-editor-primary-600: rgb(75, 85, 99);
        /* base-600 - Hover states */
        --json-editor-primary-700: rgb(55, 65, 81);
        /* base-700 - Active states */
        --json-editor-primary-800: rgb(31, 41, 55);
        /* base-800 - Dark elements */
        --json-editor-primary-900: rgb(17, 24, 39);
        /* base-900 - Darkest elements */

        /* Professional Accent Colors */
        --json-editor-accent-blue: rgb(59, 130, 246);
        /* Professional blue accent */
        --json-editor-accent-blue-hover: rgb(37, 99, 235);
        /* Blue hover state */
        --json-editor-accent-blue-light: rgb(147, 197, 253);
        /* Light blue for backgrounds */

        /* UNFOLD Secondary Colors (for delete/danger actions) */
        --json-editor-danger-500: rgb(239, 68, 68);
        /* Red for delete buttons */
        --json-editor-danger-600: rgb(220, 38, 38);
        /* Red hover state */
        --json-editor-danger-50: rgb(254, 242, 242);
        /* Light red background */

        /* UNFOLD Dark Mode Colors - Professional Dark Theme */
        --json-editor-dark-50: rgb(15, 23, 42);
        /* Darkest background */
        --json-editor-dark-100: rgb(30, 41, 59);
        /* Dark background */
        --json-editor-dark-200: rgb(51, 65, 85);
        /* Card backgrounds */
        --json-editor-dark-300: rgb(71, 85, 105);
        /* Active elements */
        --json-editor-dark-400: rgb(100, 116, 139);
        /* Borders */
        --json-editor-dark-500: rgb(148, 163, 184);
        /* Text */
        --json-editor-dark-600: rgb(203, 213, 225);
        /* Light text */

        /* UNFOLD Base Colors (keeping original structure) */
        --unfold-base-50: rgb(249, 250, 251);
        --unfold-base-100: rgb(243, 244, 246);
        --unfold-base-200: rgb(229, 231, 235);
        --unfold-base-300: rgb(209, 213, 219);
        --unfold-base-400: rgb(156, 163, 175);
        --unfold-base-500: rgb(107, 114, 128);
        --unfold-base-600: rgb(75, 85, 99);
        --unfold-base-700: rgb(55, 65, 81);
        --unfold-base-800: rgb(31, 41, 55);
        --unfold-base-900: rgb(17, 24, 39);

        /* Error Colors */
        --unfold-error-light: rgb(239, 68, 68);
        --unfold-error-dark: rgb(185, 28, 28);
        --unfold-error-bg-dark: rgb(252, 165, 165);

        /* Professional Shadows */
        --json-editor-shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
        --json-editor-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
        --json-editor-shadow-md: 0 4px 6px rgba(0, 0, 0, 0.07), 0 2px 4px rgba(0, 0, 0, 0.06);
        --json-editor-shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05);
        --json-editor-shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1), 0 10px 10px rgba(0, 0, 0, 0.04);

        /* Border Radius */
        --json-editor-radius-sm: 0.25rem;
        --json-editor-radius: 0.375rem;
        --json-editor-radius-md: 0.5rem;
        --json-editor-radius-lg: 0.75rem;

        /* Spacing */
        --json-editor-space-1: 0.25rem;
        --json-editor-space-2: 0.5rem;
        --json-editor-space-3: 0.75rem;
        --json-editor-space-4: 1rem;
        --json-editor-space-5: 1.25rem;
        --json-editor-space-6: 1.5rem;
    }

    /* ========================================
     * CONTAINER & LAYOUT STYLING - PROFESSIONAL THEME
     * ======================================== */

    /* Main JSON Editor Container - Professional Design */
    .json-editor-container {
        margin-bottom: var(--json-editor-space-6);
        background-color: transparent;
        border-radius: 0;
        overflow: hidden;
        width: 100%;
        box-shadow: none;
        border: none;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
    }

    .json-editor-container:hover {
        box-shadow: none;
        border-color: transparent;
        transform: none;
    }

    .json-editor-container:focus-within {
        box-shadow: none;
        border-color: transparent;
    }

    /* Editor container */
    .json-view-editor {
        width: 100%;
        border: none;
        padding: var(--json-editor-space-6);
        background-color: transparent;
        position: relative;
    }

    /* Card styling - Professional Clean Design */
    .card {
        border: none !important;
        margin-bottom: var(--json-editor-space-5) !important;
        background-color: transparent !important;
        box-shadow: none !important;
    }

    .card-body {
        padding: var(--json-editor-space-4) 0 !important;
        background-color: transparent !important;
    }

    /* Hide unnecessary elements */
    .json-view-editor .card-title {
        display: none !important;
    }

    /* Modern JSON Editor base styling */
    .json-editor-modern {
        border: none !important;
        box-shadow: none !important;
        background-color: transparent !important;
    }

    .jsoneditor-menu {
        display: none !important;
    }

    /* Modern card styling - Professional consistency */
    .modern-card {
        border: none !important;
        box-shadow: none !important;
        background: transparent !important;
        margin-bottom: var(--json-editor-space-4) !important;
        padding: 0 !important;
        width: 100% !important;
        border-radius: var(--json-editor-radius) !important;
    }

    .card-body {
        padding: 0 !important;
        width: 100% !important;
        background-color: transparent !important;
    }

    /* ========================================
     * TABLE STYLING - PROFESSIONAL DESIGN
     * ======================================== */

    /* Table layout */
    .full-width-table {
        width: 100% !important;
        margin-bottom: var(--json-editor-space-4) !important;
        border-collapse: separate !important;
        border-spacing: 0 !important;
    }

    .table-responsive {
        border: 1px solid var(--json-editor-primary-200);
        border-radius: var(--json-editor-radius-md);
        overflow: hidden;
        margin-bottom: var(--json-editor-space-5);
        background-color: white;
        box-shadow: var(--json-editor-shadow);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .table-responsive:hover {
        box-shadow: var(--json-editor-shadow-md);
        border-color: var(--json-editor-primary-300);
        transform: translateY(-1px);
    }

    .equal-width-cell {
        width: 45% !important;
    }

    .table-controls-cell {
        width: 10% !important;
        text-align: right !important;
    }

    .modern-table-header {
        background: linear-gradient(135deg, var(--json-editor-primary-50) 0%, var(--json-editor-primary-100) 100%) !important;
        color: var(--json-editor-primary-800) !important;
        font-weight: 600 !important;
        text-transform: uppercase !important;
        font-size: 0.6875rem !important;
        letter-spacing: 0.05em !important;
        padding: var(--json-editor-space-4) var(--json-editor-space-4) !important;
        border-bottom: 1px solid var(--json-editor-primary-200) !important;
        position: relative;
    }

    .modern-table-row {
        border-bottom: 1px solid var(--json-editor-primary-100) !important;
        transition: all 0.2s ease;
    }

    .modern-table-row:last-child {
        border-bottom: none !important;
    }

    .modern-table-row:hover {
        background-color: var(--json-editor-primary-50) !important;
        transform: translateX(2px);
    }

    .modern-table-row td {
        padding: var(--json-editor-space-4) var(--json-editor-space-4) !important;
        vertical-align: middle !important;
        font-size: 0.875rem !important;
        color: var(--json-editor-primary-700) !important;
        font-weight: 500 !important;
    }

    /* ========================================
     * BUTTON STYLING - PROFESSIONAL DESIGN
     * ======================================== */

    /* Modern buttons - Professional color scheme */
    .json-editor-btn-modern {
        display: inline-flex !important;
        align-items: center !important;
        justify-content: center !important;
        gap: var(--json-editor-space-2) !important;
        background: linear-gradient(135deg, var(--json-editor-primary-600) 0%, var(--json-editor-primary-700) 100%) !important;
        color: white !important;
        border: none !important;
        border-radius: var(--json-editor-radius) !important;
        padding: var(--json-editor-space-3) var(--json-editor-space-4) !important;
        font-weight: 600 !important;
        cursor: pointer !important;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
        box-shadow: var(--json-editor-shadow) !important;
        margin: 0 var(--json-editor-space-1) !important;
        font-size: 0.875rem !important;
        line-height: 1.5 !important;
        text-transform: none !important;
        letter-spacing: 0.025em !important;
        position: relative !important;
        overflow: hidden !important;
    }

    .json-editor-btn-modern::before {
        content: '' !important;
        position: absolute !important;
        top: 0 !important;
        left: -100% !important;
        width: 100% !important;
        height: 100% !important;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent) !important;
        transition: left 0.5s !important;
    }

    .json-editor-btn-modern:hover {
        background: linear-gradient(135deg, var(--json-editor-primary-700) 0%, var(--json-editor-primary-800) 100%) !important;
        transform: translateY(-2px) !important;
        box-shadow: var(--json-editor-shadow-lg) !important;
    }

    .json-editor-btn-modern:hover::before {
        left: 100% !important;
    }

    .json-editor-btn-modern:active {
        transform: translateY(0) !important;
        box-shadow: var(--json-editor-shadow) !important;
    }

    .json-editor-btn-modern:focus {
        outline: none !important;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3) !important;
    }

    /* Button sections styling - Professional theme */
    .json-editor-btngroup,
    .json-editor-btn-bar {
        display: flex !important;
        flex-wrap: wrap !important;
        gap: var(--json-editor-space-3) !important;
        margin-top: var(--json-editor-space-5) !important;
        margin-bottom: 0 !important;
        padding: 0 !important;
        background: transparent !important;
        border: none !important;
        border-radius: 0 !important;
        visibility: visible !important;
        width: 100% !important;
        justify-content: flex-end !important;
        box-shadow: none !important;
        position: relative !important;
    }

    .json-editor-btngroup::before,
    .json-editor-btn-bar::before {
        display: none !important;
    }

    /* Button icons */
    .json-editor-btn-icon {
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
    }

    /* Add button styling - Primary action with accent */
    .json-editor-btn-add {
        background: linear-gradient(135deg, var(--json-editor-accent-blue) 0%, var(--json-editor-accent-blue-hover) 100%) !important;
        padding: var(--json-editor-space-3) var(--json-editor-space-5) !important;
        font-size: 0.875rem !important;
        font-weight: 600 !important;
        letter-spacing: 0.025em !important;
        border-radius: var(--json-editor-radius) !important;
        width: auto !important;
        margin-bottom: var(--json-editor-space-3) !important;
        box-shadow: var(--json-editor-shadow-md) !important;
    }

    .json-editor-btn-add:hover {
        background: linear-gradient(135deg, var(--json-editor-accent-blue-hover) 0%, var(--json-editor-primary-800) 100%) !important;
        transform: translateY(-2px) !important;
        box-shadow: var(--json-editor-shadow-lg) !important;
    }

    /* Delete button styling - Danger action */
    .json-editor-btn-delete {
        background: linear-gradient(135deg, var(--json-editor-danger-500) 0%, var(--json-editor-danger-600) 100%) !important;
    }

    .json-editor-btn-delete:hover {
        background: linear-gradient(135deg, var(--json-editor-danger-600) 0%, rgb(185, 28, 28) 100%) !important;
    }

    /* Modern HTML5-like error styling */
    .je-error-container {
        display: none !important;
        /* Hide the default error container */
    }

    /* ========================================
     * FORM CONTROLS - PROFESSIONAL DESIGN
     * ======================================== */

    /* Form controls - Professional theme */
    .modern-form-control {
        width: 100% !important;
        border: 1px solid var(--json-editor-primary-300) !important;
        border-radius: var(--json-editor-radius) !important;
        padding: var(--json-editor-space-3) var(--json-editor-space-4) !important;
        font-size: 0.875rem !important;
        line-height: 1.6 !important;
        color: var(--json-editor-primary-800) !important;
        background-color: white !important;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
        appearance: none !important;
        margin-bottom: var(--json-editor-space-4) !important;
        box-shadow: var(--json-editor-shadow-sm) !important;
        font-weight: 500 !important;
    }

    .modern-form-control:hover {
        border-color: var(--json-editor-primary-400) !important;
        box-shadow: var(--json-editor-shadow) !important;
    }

    .modern-form-control:focus {
        border-color: var(--json-editor-accent-blue) !important;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.15) !important;
        outline: none !important;
        transform: translateY(-1px) !important;
    }

    .modern-form-control::placeholder {
        color: var(--json-editor-primary-400) !important;
        font-weight: 400 !important;
    }

    /* Form labels - Professional styling */
    label,
    .je-label {
        font-size: 0.875rem !important;
        font-weight: 600 !important;
        color: var(--json-editor-primary-800) !important;
        margin-bottom: var(--json-editor-space-2) !important;
        display: block !important;
        text-transform: uppercase !important;
        letter-spacing: 0.025em !important;
    }

    /* Form groups */
    .form-group,
    .je-object__container {
        margin-bottom: var(--json-editor-space-6) !important;
        width: 100% !important;
        position: relative !important;
    }

    /* Style for invalid inputs - Professional error design */
    .je-error+input,
    .je-error+select,
    .je-error+textarea,
    .has-error .modern-form-control {
        border-color: var(--json-editor-danger-500) !important;
        padding-right: 2.5rem !important;
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23ef4444' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='10'%3E%3C/circle%3E%3Cline x1='12' y1='8' x2='12' y2='12'%3E%3C/line%3E%3Cline x1='12' y1='16' x2='12.01' y2='16'%3E%3C/line%3E%3C/svg%3E");
        background-repeat: no-repeat;
        background-position: right var(--json-editor-space-3) center;
        background-size: 1.25rem;
        background-color: var(--json-editor-danger-50) !important;
        animation: shake 0.3s ease-in-out !important;
    }

    @keyframes shake {

        0%,
        100% {
            transform: translateX(0);
        }

        25% {
            transform: translateX(-2px);
        }

        75% {
            transform: translateX(2px);
        }
    }

    /* ========================================
     * ERROR STYLING - UNFOLD INTEGRATION
     * ======================================== */

    /* Modern HTML5-like error styling */
    .je-error-container {
        display: none !important;
        /* Hide the default error container */
    }

    /* Custom tooltip for errors */
    .je-error {
        position: relative !important;
        display: inline-block !important;
        color: transparent !important;
        font-size: 0 !important;
        width: 0 !important;
        height: 0 !important;
        overflow: visible !important;
    }

    .je-error::after {
        content: attr(data-content) !important;
        position: absolute !important;
        bottom: 125% !important;
        right: 0 !important;
        visibility: hidden !important;
        width: 220px !important;
        background: linear-gradient(135deg, var(--json-editor-danger-500) 0%, var(--json-editor-danger-600) 100%) !important;
        color: white !important;
        text-align: center !important;
        border-radius: var(--json-editor-radius) !important;
        padding: var(--json-editor-space-3) var(--json-editor-space-4) !important;
        font-size: 0.8125rem !important;
        font-weight: 600 !important;
        opacity: 0 !important;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
        z-index: 100 !important;
        pointer-events: none !important;
        box-shadow: var(--json-editor-shadow-lg) !important;
        border: 1px solid rgba(255, 255, 255, 0.2) !important;
    }

    /* Show tooltip on hover over the input */
    .je-error+input:hover+.je-error::after,
    .je-error+select:hover+.je-error::after,
    .je-error+textarea:hover+.je-error::after,
    .has-error .modern-form-control:hover+.je-error::after {
        visibility: visible !important;
        opacity: 1 !important;
    }

    /* Arrow for tooltip */
    .je-error::before {
        content: "" !important;
        position: absolute !important;
        bottom: 125% !important;
        right: 15px !important;
        visibility: hidden !important;
        border-width: 6px !important;
        border-style: solid !important;
        border-color: var(--json-editor-danger-500) transparent transparent transparent !important;
        opacity: 0 !important;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
        filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1)) !important;
    }

    .je-error+input:hover+.je-error::before,
    .je-error+select:hover+.je-error::before,
    .je-error+textarea:hover+.je-error::before,
    .has-error .modern-form-control:hover+.je-error::before {
        visibility: visible !important;
        opacity: 1 !important;
    }

    /* Error message styling - Professional */
    .invalid-feedback {
        color: var(--json-editor-danger-600) !important;
        font-size: 0.875rem !important;
        font-weight: 500 !important;
        margin-top: var(--json-editor-space-1) !important;
        margin-bottom: var(--json-editor-space-2) !important;
        padding: var(--json-editor-space-2) var(--json-editor-space-3) !important;
        background-color: var(--json-editor-danger-50) !important;
        border-radius: var(--json-editor-radius-sm) !important;
        border-left: 3px solid var(--json-editor-danger-500) !important;
    }

    /* ========================================
     * ADDITIONAL BUTTON TYPES - PROFESSIONAL
     * ======================================== */

    /* Hide Delete Last buttons */
    .json-editor-btntype-deletelast {
        display: none !important;
    }

    /* Move up/down buttons - Professional styling */
    .json-editor-btntype-moveup,
    .json-editor-btntype-movedown {
        background: linear-gradient(135deg, var(--json-editor-primary-500) 0%, var(--json-editor-primary-600) 100%) !important;
        border: 1px solid var(--json-editor-primary-400) !important;
    }

    .json-editor-btntype-moveup:hover,
    .json-editor-btntype-movedown:hover {
        background: linear-gradient(135deg, var(--json-editor-primary-600) 0%, var(--json-editor-primary-700) 100%) !important;
        border-color: var(--json-editor-primary-500) !important;
    }

    /* Modern button group styling */
    .modern-btn-group {
        display: inline-flex !important;
        gap: var(--json-editor-space-2) !important;
        margin: var(--json-editor-space-1) !important;
        align-items: center !important;
        justify-content: flex-end !important;
        padding: 0 !important;
        background-color: transparent !important;
        border-radius: 0 !important;
        border: none !important;
    }

    /* ========================================
     * RESPONSIVE DESIGN - PROFESSIONAL MOBILE
     * ======================================== */

    @media (max-width: 767px) {
        .json-editor-container {
            margin-bottom: var(--json-editor-space-4);
            border-radius: var(--json-editor-radius);
            box-shadow: var(--json-editor-shadow);
        }

        .json-view-editor {
            padding: var(--json-editor-space-4);
        }

        .modern-form-control {
            font-size: 0.875rem !important;
            padding: var(--json-editor-space-3) var(--json-editor-space-3) !important;
        }

        .json-editor-btn-modern {
            padding: var(--json-editor-space-2) var(--json-editor-space-3) !important;
            font-size: 0.875rem !important;
            margin: 0 var(--json-editor-space-1) var(--json-editor-space-1) 0 !important;
            min-height: 44px !important;
            /* Touch-friendly */
        }

        .modern-table-header {
            font-size: 0.75rem !important;
            padding: var(--json-editor-space-3) var(--json-editor-space-3) !important;
        }

        .modern-table-row td {
            padding: var(--json-editor-space-3) var(--json-editor-space-3) !important;
            font-size: 0.8125rem !important;
        }

        .json-editor-btngroup,
        .json-editor-btn-bar {
            padding: var(--json-editor-space-3) !important;
            gap: var(--json-editor-space-2) !important;
            flex-direction: column !important;
            align-items: stretch !important;
        }

        .json-editor-btn-modern {
            width: 100% !important;
            justify-content: center !important;
            min-height: 48px !important;
        }

        /* Mobile-specific enhancements */
        .table-responsive {
            border-radius: var(--json-editor-radius) !important;
            overflow-x: auto !important;
        }

        .modern-btn-group {
            flex-direction: column !important;
            gap: var(--json-editor-space-1) !important;
        }
    }

    /* ========================================
     * DARK MODE SUPPORT - PROFESSIONAL DARK THEME
     * ======================================== */

    @media (prefers-color-scheme: dark) {

        /* Container styling in dark mode */
        .json-editor-container {
            background-color: transparent !important;
            border-color: transparent !important;
            box-shadow: none;
        }

        .json-editor-container:hover {
            border-color: var(--json-editor-accent-blue) !important;
            box-shadow: var(--json-editor-shadow-xl);
        }

        .json-editor-container:focus-within {
            border-color: var(--json-editor-accent-blue) !important;
        }

        .json-view-editor {
            background-color: var(--json-editor-dark-100) !important;
        }

        /* Table styling in dark mode */
        .table-responsive {
            border-color: var(--json-editor-dark-300) !important;
            background-color: var(--json-editor-dark-100) !important;
        }

        .table-responsive:hover {
            border-color: var(--json-editor-dark-400) !important;
        }

        .modern-table-header {
            background: linear-gradient(135deg, var(--json-editor-dark-200) 0%, var(--json-editor-dark-300) 100%) !important;
            color: var(--json-editor-dark-600) !important;
            border-bottom-color: var(--json-editor-dark-400) !important;
        }

        .modern-table-row {
            border-bottom-color: var(--json-editor-dark-300) !important;
        }

        .modern-table-row:hover {
            background-color: var(--json-editor-dark-200) !important;
        }

        .modern-table-row td {
            color: var(--json-editor-dark-500) !important;
        }

        /* Form controls in dark mode */
        .modern-form-control {
            background-color: var(--json-editor-dark-200) !important;
            border-color: var(--json-editor-dark-300) !important;
            color: var(--json-editor-dark-600) !important;
        }

        .modern-form-control:hover {
            border-color: var(--json-editor-dark-400) !important;
        }

        .modern-form-control:focus {
            border-color: var(--json-editor-accent-blue) !important;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.25) !important;
        }

        .modern-form-control::placeholder {
            color: var(--json-editor-dark-400) !important;
        }

        /* Labels in dark mode */
        label,
        .je-label {
            color: var(--json-editor-dark-600) !important;
        }

        /* Button sections in dark mode */
        .json-editor-btngroup,
        .json-editor-btn-bar {
            background: transparent !important;
            border: none !important;
            box-shadow: none !important;
        }

        .json-editor-btngroup::before,
        .json-editor-btn-bar::before {
            display: none !important;
        }

        /* Error styling in dark mode */
        .je-error+input,
        .je-error+select,
        .je-error+textarea,
        .has-error .modern-form-control {
            border-color: var(--json-editor-danger-500) !important;
            background-color: rgba(239, 68, 68, 0.1) !important;
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23ef4444' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='10'%3E%3C/circle%3E%3Cline x1='12' y1='8' x2='12' y2='12'%3E%3C/line%3E%3Cline x1='12' y1='16' x2='12.01' y2='16'%3E%3C/line%3E%3C/svg%3E");
        }

        .je-error::after {
            background: linear-gradient(135deg, var(--json-editor-danger-600) 0%, rgb(185, 28, 28) 100%) !important;
            color: white !important;
            box-shadow: var(--json-editor-shadow-xl) !important;
        }

        .je-error::before {
            border-color: var(--json-editor-danger-600) transparent transparent transparent !important;
        }

        /* Card styling in dark mode */
        .modern-card {
            background-color: var(--json-editor-dark-100) !important;
        }

        /* Error messages in dark mode */
        .invalid-feedback {
            color: var(--json-editor-danger-500) !important;
            background-color: rgba(239, 68, 68, 0.1) !important;
            border-left-color: var(--json-editor-danger-500) !important;
        }

        /* Modern button group in dark mode */
        .modern-btn-group {
            background-color: transparent !important;
            border-color: transparent !important;
        }
    }
</style>