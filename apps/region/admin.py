from django.contrib import admin
from django.urls import reverse
from django.utils.html import format_html
from django.utils.translation import gettext_lazy as _
from ajaxdatatable.admin import AjaxDatatable
from django import forms

from unfold.admin import ModelAdmin, TabularInline
from unfold.decorators import display, action

from apps.region.models import Region, UserRegion, InvitationLink
from utils.admin import project_admin_site, SuperAdminOnlyAdmin, RegionFilteredAdmin
from utils import create_copy_field_with_button, gregorian_to_jalali

class UserRegionInline(TabularInline):
    """Inline for displaying UserRegions in Region admin - all fields read-only"""
    model = UserRegion
    extra = 0  # No extra empty forms
    # can_delete = False
    tab = True
    fields = ('user', 'invited_by', 'is_active', 'jalali_created_at',)
    readonly_fields = ('user', 'invited_by', 'is_active', 'jalali_created_at',)

    def jalali_created_at(self, obj):
        from utils import gregorian_to_jalali
        return gregorian_to_jalali(obj.created_at, "%Y/%m/%d %H:%M") if getattr(obj, 'created_at', None) else "-"
    jalali_created_at.short_description = UserRegion._meta.get_field('created_at').verbose_name
    
    def has_add_permission(self, request, obj=None):
        # Disable adding new InvitationLink entries
        return False
    
    def has_change_permission(self, request, obj=None):
        # Disable editing InvitationLink entries
        return False
    
    def has_delete_permission(self, request, obj=None):
        # Disable deleting InvitationLink entries
        return False

from django.utils.safestring import mark_safe
from django.urls import reverse
from unfold.widgets import UnfoldAdminTextInputWidget
from django.contrib.admin.widgets import AutocompleteSelect

class NoButtonsAutocompleteSelect(AutocompleteSelect):
    """Custom autocomplete widget that hides related object buttons"""

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Disable all related object buttons
        self.can_add_related = False
        self.can_change_related = False
        self.can_delete_related = False
        self.can_view_related = False

class RegionAdminForm(forms.ModelForm):
    # Dynamic fields for owner email and password management
    owner_email = forms.EmailField(
        required=False,
        label="Owner Email",
        help_text="Email address of the selected owner (editable)",
        widget=UnfoldAdminTextInputWidget(attrs={
            'placeholder': 'Enter owner email address...',
            'class': 'dynamic-field',
            'style': 'display: none;'  # Initially hidden
        })
    )

    owner_password_display = forms.CharField(
        required=False,
        label="Owner Password",
        help_text="Current password (read-only)",
        widget=UnfoldAdminTextInputWidget(attrs={
            'readonly': True,
            'value': '********',
            'class': 'dynamic-field',
            'style': 'display: none;'  # Initially hidden
        })
    )

    class Meta:
        model = Region
        fields = '__all__'

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['owner'].help_text = "Select an owner to manage their email and password"

        # If editing existing region with owner, populate email field
        if self.instance and self.instance.pk and self.instance.owner:
            self.fields['owner_email'].initial = self.instance.owner.email or ''
            self.fields['owner_email'].widget.attrs['style'] = ''  # Show field
            self.fields['owner_password_display'].widget.attrs['style'] = ''  # Show field

            # Add password change link to help text
            if self.instance.owner.pk:
                password_change_url = reverse('admin:auth_user_password_change', args=[self.instance.owner.pk])
                self.fields['owner_password_display'].help_text = mark_safe(
                    f'Current password (read-only) - <a href="{password_change_url}" target="_blank" '
                    f'class="text-primary-600 hover:text-primary-700">Change Password</a>'
                )

    def clean_owner_email(self):
        """Validate owner email field"""
        email = self.cleaned_data.get('owner_email')
        owner = self.cleaned_data.get('owner')

        if owner and email:
            # Check if email is unique (excluding current owner)
            from apps.account.models import User
            existing_user = User.objects.filter(email=email).exclude(pk=owner.pk).first()
            if existing_user:
                raise forms.ValidationError("This email address is already in use by another user.")

        return email

    def save(self, commit=True):
        """Override save to handle email updates"""
        instance = super().save(commit=False)

        # Update owner's email if changed
        owner_email = self.cleaned_data.get('owner_email')
        if instance.owner and owner_email and instance.owner.email != owner_email:
            instance.owner.email = owner_email
            if commit:
                instance.owner.save()

        if commit:
            instance.save()

        return instance

class InvitationLinkInline(TabularInline):
    """Inline for displaying InvitationLinks in UserRegion admin"""
    model = InvitationLink
    extra = 0
    tab = True
    can_delete = False  # Disable deletion
    fields = ('invitation_url_with_copy', 'is_used', 'used_by', 'jalali_created_at', 'used_at')
    readonly_fields = ('used_by', 'is_used', 'invitation_url_with_copy', 'jalali_created_at', 'used_at')

    def jalali_created_at(self, obj):
        from utils import gregorian_to_jalali
        return gregorian_to_jalali(obj.created_at, "%Y/%m/%d %H:%M") if getattr(obj, 'created_at', None) else "-"
    jalali_created_at.short_description = InvitationLink._meta.get_field('created_at').verbose_name
    
    def invitation_url_with_copy(self, obj):
        """Display invitation URL with copy button"""
        if obj and obj.full_invitation_url:
            return create_copy_field_with_button(obj.full_invitation_url, f"inline_{obj.id}", "کپی")
        return '-'
    invitation_url_with_copy.short_description = _("Invitation URL")
    
    def has_add_permission(self, request, obj=None):
        # Disable adding new InvitationLink entries
        return False
    
    def has_change_permission(self, request, obj=None):
        # Disable editing InvitationLink entries
        return False
    
    def has_delete_permission(self, request, obj=None):
        # Disable deleting InvitationLink entries
        return False


@admin.register(Region)
class RegionAdmin(RegionFilteredAdmin):
    form = RegionAdminForm
    list_display = ('name', 'owner','members_count', 'created_at',)
    search_fields = ('name',)
    list_filter = ('created_at', 'updated_at')
    inlines = [InvitationLinkInline, UserRegionInline]
    autocomplete_fields = ('owner',)

    class Media:
        js = [
            'admin/js/vendor/jquery/jquery.js',
            'admin/js/jquery.init.js',
            'js/region_admin_dynamic.js',
        ]
        css = {
            'all': ('css/region_admin_dynamic.css',)
        }

    def get_queryset(self, request):
        """Show only regions owned by this user if region owner; superuser sees all"""
        qs = super().get_queryset(request)
        if request.user.is_superuser:
            return qs
        # If user is region owner, limit to owned regions
        if hasattr(request.user, 'owned_regions') and request.user.owned_regions.exists():
            return qs.filter(owner=request.user)
        # Otherwise, hide regions
        return qs.none()

    def get_readonly_fields(self, request, obj=None):
        """If the admin is RegionFilteredAdmin and user is region owner, make owner read-only"""
        ro = list(super().get_readonly_fields(request, obj))
        if not request.user.is_superuser:
            if hasattr(request.user, 'owned_regions') and request.user.owned_regions.exists():
                ro.append('owner')
        return ro
    history = False
    actions_detail = [
        "generate_invitation_code",
    ]

    fieldsets = (
        (None, {
            "fields": ("name", 'owner'),
        }),
        (_("Owner Management"), {
            "fields": ("owner_email", "owner_password_display"),
            "classes": ("dynamic-owner-fields",),
        }),
    )
    def formfield_for_foreignkey(self, db_field, request, **kwargs):
        if db_field.name == 'owner':
            # Use custom widget that hides buttons but preserves autocomplete
            kwargs['widget'] = NoButtonsAutocompleteSelect(
                db_field,
                self.admin_site,
                attrs={'data-placeholder': 'Select owner...'}
            )
        return super().formfield_for_foreignkey(db_field, request, **kwargs)
    @display(description=Region._meta.get_field('created_at').verbose_name, ordering='created_at')
    def created_at(self, obj):
        from utils import gregorian_to_jalali
        if obj.created_at:
            return gregorian_to_jalali(obj.created_at, "%Y/%m/%d %H:%M")
        return "-"
    
    @display(description=_("Members Count"))
    def members_count(self, obj):
        # Create a link to the User admin filtered by this region
        url = reverse('admin:account_user_changelist') + f'?region_memberships__region={obj.id}'
        count = obj.members.filter(is_active=True).count()
        return format_html(
            '<a href="{}"><i class="material-icons" style="font-size: 16px;">{} </i></a>',
            url, count
        )



    @action(description=_("Generate Invitation Code"))
    def generate_invitation_code(self, request, object_id):
        """Generate invitation code for current user in this region"""
        from django.http import HttpResponseRedirect
        from django.urls import reverse
        
        # Get the region object
        obj = self.get_object(request, object_id)
        if obj is None:
            self.message_user(request, _("Region not found"), level='ERROR')
            return HttpResponseRedirect(reverse('admin:region_region_changelist'))
            
        # Check if UserRegion exists for the current user and selected region
        user_region = UserRegion.objects.filter(
            user=request.user,
            region=obj
        ).first()
        
        # If UserRegion doesn't exist, create a new one
        if not user_region:
            user_region = UserRegion.objects.create(
                user=request.user,
                region=obj,
                is_active=True,
                is_current=False
            )
        
        # Create new invitation link
        invitation_link = InvitationLink.objects.create(
            user_region=user_region,
            region=obj
        )
        
        # Generate invitation code
        invitation_link.invitation_code = invitation_link.generate_invitation_code()
        invitation_link.save()
        
        self.message_user(
            request,
            _("Invitation code generated successfully: {}").format(invitation_link.full_invitation_url)
        )
        
        # Redirect back to the region change page
        return HttpResponseRedirect(reverse('admin:region_region_change', args=[object_id]))
        

class UserRegionForm(forms.ModelForm):
    invitation_code_input = forms.CharField(
        required=False,
        help_text=_("Enter invitation code to validate and set region/invited_by automatically"),
        label=_("Invitation Code"),
        widget=forms.TextInput(attrs={'placeholder': 'Enter invitation code...'})
    )
    
    class Meta:
        model = UserRegion
        fields = '__all__'
    
    def clean_invitation_code_input(self):
        invitation_code = self.cleaned_data.get('invitation_code_input')
        
        if not invitation_code:
            return invitation_code
        
        # Validate using the model method
        is_valid, region, invited_by, error_message = UserRegion.validate_invitation_code(invitation_code)
        
        if not is_valid:
            raise forms.ValidationError(error_message)
        
        # Store validated data for use in save
        self.validated_region = region
        self.validated_invited_by = invited_by
        
        return invitation_code


@admin.register(UserRegion)
class UserRegionAdmin(RegionFilteredAdmin):
    form = UserRegionForm
    list_display = ('user', 'region', 'invited_by', 'is_active', 'created_at')

    def get_queryset(self, request):
        """Limit to memberships of regions owned by current region owner"""
        qs = super().get_queryset(request)
        if request.user.is_superuser:
            return qs
        if hasattr(request.user, 'owned_regions') and request.user.owned_regions.exists():
            return qs.filter(region__owner=request.user)
        return qs.none()
    search_fields = ('user__fullname', 'region__name')
    list_filter = ('is_active', 'is_current', 'region')
    readonly_fields = ('is_current', )
    autocomplete_fields = ('user', 'region', 'invited_by')
    inlines = [InvitationLinkInline]
    
    fieldsets = (
        (None, {
            "fields": ("user",),
        }),
        (_("Region Information"), {
            "fields": ("region", "invited_by", "is_active"),
        }),
    )

    @display(description=UserRegion._meta.get_field('created_at').verbose_name, ordering='created_at')
    def created_at(self, obj):
        from utils import gregorian_to_jalali
        return gregorian_to_jalali(obj.created_at, "%Y/%m/%d %H:%M") if obj.created_at else "-"
    
    def save_model(self, request, obj, form, change):
        """Override save_model to handle invitation code validation"""
        invitation_code = form.cleaned_data.get('invitation_code_input')
        
        if invitation_code and not change:  # Only for new objects
            # Use validated data from form
            if hasattr(form, 'validated_region') and hasattr(form, 'validated_invited_by'):
                obj.region = form.validated_region
                obj.invited_by = form.validated_invited_by
                
                # Check if user is already a member of this region
                existing_membership = UserRegion.objects.filter(
                    user=obj.user, 
                    region=obj.region
                ).first()
                
                if existing_membership:
                    self.message_user(
                        request,
                        _("User is already a member of this region."),
                        level='ERROR'
                    )
                    return
                
                # Mark invitation link as used if it exists
                try:
                    from apps.region.models import InvitationLink
                    invitation_link = InvitationLink.objects.get(invitation_code=invitation_code.split('/')[-1])
                    if not invitation_link.is_used:
                        invitation_link.mark_as_used(obj.user)
                        self.message_user(
                            request,
                            _("User successfully added to region using invitation code.")
                        )
                except InvitationLink.DoesNotExist:
                    pass  # It's okay, might be a UserRegion or Region invitation code
        
        super().save_model(request, obj, form, change)


class InvitationLinkAdmin(ModelAdmin):
    list_display = ('region', 'is_used', 'created_at', 'invitation_url_display')
    search_fields = ('invitation_code', 'user_region__user__fullname', 'used_by__fullname', 'region__name')
    list_filter = ('is_used', 'region', 'created_at', 'used_at')
    autocomplete_fields = ('region', 'used_by')

    def get_queryset(self, request):
        """Limit invitation links to regions owned by current region owner"""
        qs = super().get_queryset(request)
        if request.user.is_superuser:
            return qs
        if hasattr(request.user, 'owned_regions') and request.user.owned_regions.exists():
            return qs.filter(region__owner=request.user)
        return qs.none()

    @display(description=InvitationLink._meta.get_field('created_at').verbose_name, ordering='created_at')
    def created_at(self, obj):
        from utils import gregorian_to_jalali
        return gregorian_to_jalali(obj.created_at, "%Y/%m/%d %H:%M") if obj.created_at else "-"
    
    def invitation_url_display(self, obj):
        """Display the full invitation URL with copy button"""
        if obj and obj.full_invitation_url:
            return create_copy_field_with_button(obj.full_invitation_url, f"admin_{obj.id}", "کپی")
        return '-'
    invitation_url_display.short_description = _("Invitation URL")
    
    def invitation_url_with_copy_readonly(self, obj):
        """Display the full invitation URL with copy button for fieldsets"""
        if obj and obj.full_invitation_url:
            return create_copy_field_with_button(obj.full_invitation_url, f"readonly_{obj.id}", "کپی")
        return '-'
    invitation_url_with_copy_readonly.short_description = _("Invitation URL")
    
    fieldsets = (
        (None, {
            "fields": ("region",),
        }),
        (_("Invitation URL"), {
            "fields": ("invitation_url_with_copy_readonly",),
        }),
        (_("information"), {
            "fields": ("is_used", "used_by", "used_at", "created_at",),
        }),
    )

    readonly_fields = ('created_at', 'used_at', 'used_by', 'is_used', 'invitation_url_with_copy_readonly')


    def save_model(self, request, obj, form, change):
        """Override save_model to automatically set user_region and invitation_code"""
        if not change:  # Only for new objects
            # Check if UserRegion exists for the current user and selected region
            user_region = UserRegion.objects.filter(
                user=request.user,
                region=obj.region
            ).first()
            
            # If UserRegion doesn't exist, create a new one
            if not user_region:
                user_region = UserRegion.objects.create(
                    user=request.user,
                    region=obj.region,
                    is_active=True,
                    is_current=False
                )
            
            obj.user_region = user_region
            
            # Generate invitation code
            obj.invitation_code = obj.generate_invitation_code()
        
        super().save_model(request, obj, form, change)





# Register with project_admin_site
project_admin_site.register(Region, RegionAdmin)
project_admin_site.register(UserRegion, UserRegionAdmin)
project_admin_site.register(InvitationLink, InvitationLinkAdmin)
