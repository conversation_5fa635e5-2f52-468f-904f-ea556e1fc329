from django.urls import path
from .views import (
    RegionListAPIView,
    ChangeUserRegionAPIView,
    UserInvitationLinksListAPIView,
    CreateInvitationLinkAPIView,
    UserRegionDetailAPIView,
    get_user_data_ajax
)

app_name = 'region'

urlpatterns = [
    path('list/', RegionListAPIView.as_view(), name='region-list'),
    path('my-region/', UserRegionDetailAPIView.as_view(), name='user-region-detail'),
    path('change/', ChangeUserRegionAPIView.as_view(), name='change-user-region'),
    path('invitation-links/', UserInvitationLinksListAPIView.as_view(), name='user-invitation-links'),
    path('create-invitation-link/', CreateInvitationLinkAPIView.as_view(), name='create-invitation-link'),
    # Admin AJAX endpoint
    path('admin/get-user-data/', get_user_data_ajax, name='admin-get-user-data'),
]
