from django.utils.translation import gettext_lazy as _
from django.contrib import admin
from django.templatetags.static import static
from django.utils.html import format_html

from unfold.admin import ModelAdmin
from unfold.decorators import display, action
from unfold.contrib.filters.admin import (
    RangeDateFilter, 
    ChoicesRadioFilter
)

from apps.issues.models import IssueReport, ReportSubject
from utils.admin import project_admin_site
from utils import gregorian_to_jalali


class IssueReportAdmin(ModelAdmin):
    """Admin for issue reports"""
    list_display = (
        'issue_info', 'user_info', 'deposit_info', 'issue_status', 'formatted_created_at'
    )
    list_filter = (
        ('status', ChoicesRadioFilter),
        ('created_at', RangeDateFilter),
        ('updated_at', RangeDateFilter),
    )
    ordering = ('-created_at',)
    readonly_fields = ('created_at', 'updated_at')
    search_fields = (
        'subject', 'description', 'user__fullname', 'user__phone_number', 'deposit__title'
    )
    list_filter_submit = True  # Enable submit button for filters
    autocomplete_fields = ('user',)
    
    fieldsets = (
        (None, {
            'fields': ('subject', 'description'),
        }),
        (_('Relationship'), {
            'fields': ('user', 'deposit'),
            'classes': ('tab',),
        }),
        (_('Status'), {
            'fields': ('status',),
            'classes': ('tab',),
        }),
        (_('Timestamps'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('tab',),
        }),
    )
    list_per_page = 10

    @display(description=_('Issue'), header=True)
    def issue_info(self, obj):
        return obj.subject
    
    @display(description=_('User'), header=True)
    def user_info(self, obj):
        if obj.user:
            
            return [
                obj.user.fullname,
            ]
        return ["-", ""]
    
    @display(description=_('Deposit'))
    def deposit_info(self, obj):
        if obj.deposit:
            return obj.deposit.title
        return "-"
    
    @display(description=_('Status'), label={
        'Open': 'warning',
        'In Progress': 'info',
        'Closed': 'success',
    })
    def issue_status(self, obj):
        status_map = {
            'Open': _('Open'),
            'In Progress': _('In Progress'),
            'Closed': _('Closed'),
        }
        return obj.status, status_map.get(obj.status, obj.status)
    
    @display(description=_('Created'))
    def formatted_created_at(self, obj):
        try:
            return gregorian_to_jalali(obj.created_at, date_format="%Y/%m/%d") if obj.created_at else "-"
        except:
            # Fallback if gregorian_to_jalali fails
            return obj.created_at.strftime("%Y-%m-%d") if obj.created_at else "-"
    
    @action(description=_("Mark as In Progress"))
    def mark_in_progress(self, request, queryset):
        queryset.update(status=IssueReport.ReportStatus.IN_PROGRESS)
        self.message_user(request, _("Selected issues have been marked as in progress."))
    
    @action(description=_("Mark as Closed"))
    def mark_closed(self, request, queryset):
        queryset.update(status=IssueReport.ReportStatus.CLOSED)
        self.message_user(request, _("Selected issues have been marked as closed."))
    
    @action(description=_("Reopen Issues"))
    def reopen_issues(self, request, queryset):
        queryset.update(status=IssueReport.ReportStatus.OPEN)
        self.message_user(request, _("Selected issues have been reopened."))
    
    actions = ['mark_in_progress', 'mark_closed', 'reopen_issues']


class ReportSubjectAdmin(ModelAdmin):
    """Admin for report subjects"""
    list_display = ('title', 'is_active', 'reports_count', 'formatted_created_at')
    list_filter = (
        'is_active',
        ('created_at', RangeDateFilter),
    )
    search_fields = ('title', 'description')
    ordering = ('title',)
    readonly_fields = ('created_at',)

    fieldsets = (
        (None, {
            'fields': ('title', 'description', 'is_active'),
        }),
        (_('Timestamps'), {
            'fields': ('created_at',),
            'classes': ('tab',),
        }),
    )

    @display(description=_('Reports Count'))
    def reports_count(self, obj):
        return obj.reports.count()

    @display(description=_('Created'))
    def formatted_created_at(self, obj):
        try:
            return gregorian_to_jalali(obj.created_at, date_format="%Y/%m/%d") if obj.created_at else "-"
        except:
            return obj.created_at.strftime("%Y-%m-%d") if obj.created_at else "-"


# Register with project_admin_site
# project_admin_site.register(IssueReport, IssueReportAdmin)
project_admin_site.register(ReportSubject, ReportSubjectAdmin)

# Register with default admin site
# admin.site.register(IssueReport, IssueReportAdmin)
admin.site.register(ReportSubject, ReportSubjectAdmin)