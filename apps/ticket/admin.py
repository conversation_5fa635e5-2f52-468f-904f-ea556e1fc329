from django.contrib import admin
from django.utils.translation import gettext_lazy as _
from django.utils.html import format_html
from django.urls import reverse, path
from django.http import HttpResponseRedirect
from unfold.admin import ModelAdmin, TabularInline
from unfold.decorators import display, action
from unfold.contrib.filters.admin import (
    RangeDateFilter,
)
from utils.admin import project_admin_site, RegionFilteredAdmin

from .models import Ticket, ReportTicket, TicketMessage
from .admin_views import TicketChatDetailView
from utils import gregorian_to_jalali


class TicketMessageInline(TabularInline):
    model = TicketMessage
    extra = 0
    readonly_fields = ('user', 'jalali_created_at')
    fields = ('user', 'message_type', 'content', 'content_image', 'is_read', 'jalali_created_at')
    can_delete = False
    verbose_name = _("Ticket Message")
    verbose_name_plural = _("Ticket Messages")

    def jalali_created_at(self, obj):
        return gregorian_to_jalali(obj.created_at, "%Y/%m/%d %H:%M") if getattr(obj, 'created_at', None) else "-"
    jalali_created_at.short_description = TicketMessage._meta.get_field('created_at').verbose_name

# @admin.register(Ticket)
class BaseTicketAdmin(RegionFilteredAdmin):
    def get_queryset(self, request):
        return super().get_queryset(request)
    list_display = ('subject', 'user', 'region', 'deposit', 'is_closed', 'formatted_created_at', 'messages_count', 'view_messages_link')

    # keep function name for compatibility in other places but ensure jalali format
    @display(description=_('Created'), ordering='created_at')
    def formatted_created_at(self, obj):
        try:
            return gregorian_to_jalali(obj.created_at, date_format="%Y/%m/%d %H:%M") if obj.created_at else "-"
        except Exception:
            return obj.created_at.strftime("%Y-%m-%d %H:%M") if obj.created_at else "-"

    list_filter = (
        'is_closed',
        'region',
        ('created_at', RangeDateFilter),
        ('closed_at', RangeDateFilter),
    )
    search_fields = ('subject', 'description', 'user__fullname', 'deposit__title', 'region__name')
    readonly_fields = ('created_at', 'updated_at', 'closed_at')
    inlines = [TicketMessageInline]
    ordering = ('-created_at',)
    date_hierarchy = 'created_at'
    fieldsets = (
        (_("General Information"), {
            'fields': ('subject', 'description', 'user', 'region', 'deposit')
        }),
        (_("Status"), {
            'fields': ('is_closed', 'closed_at')
        }),
        (_("Metadata"), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def has_add_permission(self, request):
        return False
    
    @display(description=_('Created'))
    def formatted_created_at(self, obj):
        try:
            return gregorian_to_jalali(obj.created_at, date_format="%Y/%m/%d") if obj.created_at else "-"
        except:
            # Fallback if gregorian_to_jalali fails
            return obj.created_at.strftime("%Y-%m-%d") if obj.created_at else "-"


    @display(
        description=_("Messages Count"),
        ordering="messages__count",
    )
    def messages_count(self, obj):
        return obj.messages.count()

    @display(
        boolean=True,
        description=_("Is Read")
    )
    def is_read(self, obj):
        """نمایش وضعیت خوانده‌شدن تیکت"""
        unread_messages = obj.messages.filter(is_read=False).exclude(user=obj.user).exists()
        return not unread_messages

    @display(
        description=_("Messages"),
        ordering="id",
    )
    def view_messages_link(self, obj):
        """ایجاد لینک به لیست پیام‌های تیکت"""
        url = reverse('admin:ticket_ticketmessage_changelist') + f'?ticket__id__exact={obj.id}'
        return format_html('<a href="{}" class="button" style="white-space:nowrap;">'
                          '<i class="material-icons"></i> {}</a>',
                          url, _("Messages"))

    def get_urls(self):
        """Add custom URLs for the admin."""
        urls = super().get_urls()
        prefix = f"{self.model._meta.app_label}_{self.model._meta.model_name}"
        custom_urls = [
            # Custom chat detail view
            path(
                '<path:object_id>/chat/',
                self.admin_site.admin_view(
                    TicketChatDetailView.as_view(model_admin=self)
                ),
                name=f'{prefix}_chat_detail',
            ),
            # Original change form view - accessible from chat view
            path(
                '<path:object_id>/edit/',
                self.admin_site.admin_view(self.original_change_view),
                name=f'{prefix}_original_change',
            ),
            # Add message to ticket
            path(
                '<path:object_id>/add-message/',
                self.admin_site.admin_view(self.add_message_view),
                name=f'{prefix}_add_message',
            ),
            # Toggle ticket status (close/open)
            path(
                '<path:object_id>/toggle-status/',
                self.admin_site.admin_view(self.toggle_status_view),
                name=f'{prefix}_toggle_status',
            ),
            # Edit message
            path(
                '<path:object_id>/edit-message/<int:message_id>/',
                self.admin_site.admin_view(self.edit_message_view),
                name=f'{prefix}_edit_message',
            ),
        ]
        return custom_urls + urls
    
    def original_change_view(self, request, object_id, form_url='', extra_context=None):
        """Original change view for Ticket."""
        # Call the parent's change_view directly to bypass our override
        return super(RegionFilteredAdmin, self).change_view(
            request, object_id, form_url, extra_context
        )
        
    def change_view(self, request, object_id, form_url='', extra_context=None):
        """Override change view to redirect to custom chat detail view."""
        # Redirect to our custom chat detail view
        url = reverse(
            f'admin:{self.model._meta.app_label}_{self.model._meta.model_name}_chat_detail',
            args=[object_id],
            current_app=self.admin_site.name,
        )
        return HttpResponseRedirect(url)
    
    def add_message_view(self, request, object_id):
        """Handle adding new messages to ticket from chat interface."""
        from django.contrib import messages
        from django.shortcuts import get_object_or_404
        
        if request.method == 'POST':
            print(f'----add_message_view-------> {object_id}///> {request.POST}')
            print(f'----FILES-------> {request.FILES}')
            ticket = get_object_or_404(Ticket, pk=object_id)
            
            # Check if user has permission to add messages to this ticket
            if not self.has_change_permission(request, ticket):
                messages.error(request, _("You don't have permission to add messages to this ticket."))
                return HttpResponseRedirect(
                    reverse(f"admin:{self.model._meta.app_label}_{self.model._meta.model_name}_chat_detail", args=[object_id])
                )
            
            # Get form data
            content = request.POST.get('content', '').strip()
            message_type = request.POST.get('message_type', 'text')
            content_image = request.FILES.get('content_image')
            
            print(f'----Extracted data---->')
            print(f'content: "{content}"')
            print(f'message_type: "{message_type}"')
            print(f'content_image: {content_image}')
            
            # Validate input
            if not content and not content_image:
                messages.error(request, _("Please provide a message or image."))
            elif message_type == 'text' and not content:
                messages.error(request, _("Text messages require content."))
            elif message_type == 'image' and not content_image:
                messages.error(request, _("Image messages require an image file."))
            else:
                try:
                    print(f'----ok--0->')
                    # Create the message
                    ticket_message = TicketMessage.objects.create(
                        ticket=ticket,
                        user=request.user,
                        content=content or '',
                        message_type=message_type,
                        content_image=content_image,
                        is_read=False
                    )
                    
                    # messages.success(request, _("Message sent successfully."))
                    return HttpResponseRedirect(
                        reverse(f"admin:{self.model._meta.app_label}_{self.model._meta.model_name}_chat_detail", args=[object_id])
                    )
                except Exception as e:
                    messages.error(request, _("Error sending message: {}").format(str(e)))
                    return HttpResponseRedirect(
                        reverse(f"admin:{self.model._meta.app_label}_{self.model._meta.model_name}_chat_detail", args=[object_id])
                    )
        # Redirect back to chat view
        return HttpResponseRedirect(
            reverse(f"admin:{self.model._meta.app_label}_{self.model._meta.model_name}_chat_detail", args=[object_id])
        )
    
    def toggle_status_view(self, request, object_id):
        """Handle opening/closing tickets from chat interface."""
        from django.contrib import messages
        from django.shortcuts import get_object_or_404
        from django.utils import timezone
        from django.http import HttpResponseRedirect
        from django.urls import reverse
        
        if request.method == 'POST':
            ticket = get_object_or_404(Ticket, pk=object_id)
            
            # Check if user has permission to modify this ticket
            if not self.has_change_permission(request, ticket):
                messages.error(request, _("You don't have permission to modify this ticket."))
                return HttpResponseRedirect(
                    reverse(f"admin:{self.model._meta.app_label}_{self.model._meta.model_name}_chat_detail", args=[object_id])
                )
            
            action = request.POST.get('action')
            
            try:
                if action == 'close' and not ticket.is_closed:
                    ticket.is_closed = True
                    ticket.closed_at = timezone.now()
                    ticket.save()
                    messages.success(request, _("Ticket closed successfully."))
                elif action == 'open' and ticket.is_closed:
                    ticket.is_closed = False
                    ticket.closed_at = None
                    ticket.save()
                    messages.success(request, _("Ticket reopened successfully."))
                else:
                    messages.info(request, _("No changes made to ticket status."))
            except Exception as e:
                messages.error(request, _("Error updating ticket status: {}").format(str(e)))
        
        # Redirect back to chat view
        return HttpResponseRedirect(
            reverse(f"admin:{self.model._meta.app_label}_{self.model._meta.model_name}_chat_detail", args=[object_id])
        )
    
    def edit_message_view(self, request, object_id, message_id):
        """Handle editing message from chat interface."""
        from django.contrib import messages
        from django.shortcuts import get_object_or_404
        from django.http import HttpResponseRedirect, JsonResponse
        from django.urls import reverse
        import json
        
        if request.method == 'POST':
            ticket = get_object_or_404(Ticket, pk=object_id)
            message = get_object_or_404(TicketMessage, pk=message_id, ticket=ticket)
            
            # Check if user has permission to edit this message
            if not self.has_change_permission(request, ticket):
                if request.headers.get('Content-Type') == 'application/json':
                    return JsonResponse({
                        'success': False,
                        'error': _("You don't have permission to edit this message.")
                    })
                messages.error(request, _("You don't have permission to edit this message."))
                return HttpResponseRedirect(
                    reverse(f"admin:{self.model._meta.app_label}_{self.model._meta.model_name}_chat_detail", args=[object_id])
                )
            
            # Only allow editing text messages
            if message.message_type != TicketMessage.MessageType.TEXT:
                if request.headers.get('Content-Type') == 'application/json':
                    return JsonResponse({
                        'success': False,
                        'error': _("Only text messages can be edited.")
                    })
                messages.error(request, _("Only text messages can be edited."))
                return HttpResponseRedirect(
                    reverse(f"admin:{self.model._meta.app_label}_{self.model._meta.model_name}_chat_detail", args=[object_id])
                )
            
            try:
                # Get new content from request
                if request.content_type == 'application/json':
                    data = json.loads(request.body)
                    new_content = data.get('content', '').strip()
                else:
                    new_content = request.POST.get('content', '').strip()
                
                if not new_content:
                    error_msg = _("Message content cannot be empty.")
                    if request.content_type == 'application/json':
                        return JsonResponse({'success': False, 'error': error_msg})
                    messages.error(request, error_msg)
                    return HttpResponseRedirect(
                        reverse(f"admin:{self.model._meta.app_label}_{self.model._meta.model_name}_chat_detail", args=[object_id])
                    )
                
                # Update the message
                message.content = new_content
                message.save()
                
                success_msg = _("Message updated successfully.")
                if request.content_type == 'application/json':
                    return JsonResponse({
                        'success': True,
                        'message': success_msg,
                        'new_content': new_content
                    })
                
                messages.success(request, success_msg)
                
            except Exception as e:
                error_msg = _("Error updating message: {}").format(str(e))
                if request.content_type == 'application/json':
                    return JsonResponse({'success': False, 'error': error_msg})
                messages.error(request, error_msg)
        
        # Redirect back to chat view for non-AJAX requests
        return HttpResponseRedirect(
            reverse(f"admin:{self.model._meta.app_label}_{self.model._meta.model_name}_chat_detail", args=[object_id])
        )





    list_filter = (
        'is_closed',
        'region',
        ('created_at', RangeDateFilter),
        ('closed_at', RangeDateFilter),
    )
    search_fields = ('subject', 'description', 'user__fullname', 'deposit__title', 'region__name')
    readonly_fields = ('created_at', 'updated_at', 'closed_at')
    inlines = [TicketMessageInline]
    ordering = ('-created_at',)
    # date_hierarchy = 'created_at'
    fieldsets = (
        (_("General Information"), {
            'fields': ('subject', 'description', 'user', 'region', 'deposit')
        }),
        (_("Status"), {
            'fields': ('is_closed', 'closed_at')
        }),
        (_("Metadata"), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def has_add_permission(self, request):
        return False
    
    @display(description=_('Created'))
    def formatted_created_at(self, obj):
        try:
            return gregorian_to_jalali(obj.created_at, date_format="%Y/%m/%d") if obj.created_at else "-"
        except:
            # Fallback if gregorian_to_jalali fails
            return obj.created_at.strftime("%Y-%m-%d") if obj.created_at else "-"


    @display(
        description=_("Messages Count"),
        ordering="messages__count",
    )
    def messages_count(self, obj):
        return obj.messages.count()

    @display(
        boolean=True,
        description=_("Is Read")
    )
    def is_read(self, obj):
        """نمایش وضعیت خوانده‌شدن تیکت"""
        unread_messages = obj.messages.filter(is_read=False).exclude(user=obj.user).exists()
        return not unread_messages

    @display(
        description=_("Messages"),
        ordering="id",
    )
    def view_messages_link(self, obj):
        """ایجاد لینک به لیست پیام‌های تیکت"""
        url = reverse('admin:ticket_ticketmessage_changelist') + f'?ticket__id__exact={obj.id}'
        return format_html('<a href="{}" class="button" style="white-space:nowrap;">'
                          '<i class="material-icons"></i> {}</a>',
                          url, _("Messages"))

    def get_urls(self):
        """Add custom URLs for the admin."""
        urls = super().get_urls()
        prefix = f"{self.model._meta.app_label}_{self.model._meta.model_name}"
        custom_urls = [
            # Custom chat detail view
            path(
                '<path:object_id>/chat/',
                self.admin_site.admin_view(
                    TicketChatDetailView.as_view(model_admin=self)
                ),
                name=f'{prefix}_chat_detail',
            ),
            # Original change form view - accessible from chat view
            path(
                '<path:object_id>/edit/',
                self.admin_site.admin_view(self.original_change_view),
                name=f'{prefix}_original_change',
            ),
            # Add message to ticket
            path(
                '<path:object_id>/add-message/',
                self.admin_site.admin_view(self.add_message_view),
                name=f'{prefix}_add_message',
            ),
            # Toggle ticket status (close/open)
            path(
                '<path:object_id>/toggle-status/',
                self.admin_site.admin_view(self.toggle_status_view),
                name=f'{prefix}_toggle_status',
            ),
            # Edit message
            path(
                '<path:object_id>/edit-message/<int:message_id>/',
                self.admin_site.admin_view(self.edit_message_view),
                name=f'{prefix}_edit_message',
            ),
        ]
        return custom_urls + urls
    
    def original_change_view(self, request, object_id, form_url='', extra_context=None):
        """Original change view for Ticket."""
        # Call the parent's change_view directly to bypass our override
        return super(RegionFilteredAdmin, self).change_view(
            request, object_id, form_url, extra_context
        )
        
    def change_view(self, request, object_id, form_url='', extra_context=None):
        """Override change view to redirect to custom chat detail view."""
        # Redirect to our custom chat detail view
        url = reverse(
            f'admin:{self.model._meta.app_label}_{self.model._meta.model_name}_chat_detail',
            args=[object_id],
            current_app=self.admin_site.name,
        )
        return HttpResponseRedirect(url)
    
    def add_message_view(self, request, object_id):
        """Handle adding new messages to ticket from chat interface."""
        from django.contrib import messages
        from django.shortcuts import get_object_or_404
        
        if request.method == 'POST':
            print(f'----add_message_view-------> {object_id}///> {request.POST}')
            print(f'----FILES-------> {request.FILES}')
            ticket = get_object_or_404(Ticket, pk=object_id)
            
            # Check if user has permission to add messages to this ticket
            if not self.has_change_permission(request, ticket):
                messages.error(request, _("You don't have permission to add messages to this ticket."))
                return HttpResponseRedirect(
                    reverse(f"admin:{self.model._meta.app_label}_{self.model._meta.model_name}_chat_detail", args=[object_id])
                )
            
            # Get form data
            content = request.POST.get('content', '').strip()
            message_type = request.POST.get('message_type', 'text')
            content_image = request.FILES.get('content_image')
            
            print(f'----Extracted data---->')
            print(f'content: "{content}"')
            print(f'message_type: "{message_type}"')
            print(f'content_image: {content_image}')
            
            # Validate input
            if not content and not content_image:
                messages.error(request, _("Please provide a message or image."))
            elif message_type == 'text' and not content:
                messages.error(request, _("Text messages require content."))
            elif message_type == 'image' and not content_image:
                messages.error(request, _("Image messages require an image file."))
            else:
                try:
                    print(f'----ok--0->')
                    # Create the message
                    ticket_message = TicketMessage.objects.create(
                        ticket=ticket,
                        user=request.user,
                        content=content or '',
                        message_type=message_type,
                        content_image=content_image,
                        is_read=False
                    )
                    
                    # messages.success(request, _("Message sent successfully."))
                    return HttpResponseRedirect(
                        reverse(f"admin:{self.model._meta.app_label}_{self.model._meta.model_name}_chat_detail", args=[object_id])
                    )
                except Exception as e:
                    messages.error(request, _("Error sending message: {}").format(str(e)))
                    return HttpResponseRedirect(
                        reverse(f"admin:{self.model._meta.app_label}_{self.model._meta.model_name}_chat_detail", args=[object_id])
                    )
        # Redirect back to chat view
        return HttpResponseRedirect(
            reverse(f"admin:{self.model._meta.app_label}_{self.model._meta.model_name}_chat_detail", args=[object_id])
        )
    
    def toggle_status_view(self, request, object_id):
        """Handle opening/closing tickets from chat interface."""
        from django.contrib import messages
        from django.shortcuts import get_object_or_404
        from django.utils import timezone
        from django.http import HttpResponseRedirect
        from django.urls import reverse
        
        if request.method == 'POST':
            ticket = get_object_or_404(Ticket, pk=object_id)
            
            # Check if user has permission to modify this ticket
            if not self.has_change_permission(request, ticket):
                messages.error(request, _("You don't have permission to modify this ticket."))
                return HttpResponseRedirect(
                    reverse(f"admin:{self.model._meta.app_label}_{self.model._meta.model_name}_chat_detail", args=[object_id])
                )
            
            action = request.POST.get('action')
            
            try:
                if action == 'close' and not ticket.is_closed:
                    ticket.is_closed = True
                    ticket.closed_at = timezone.now()
                    ticket.save()
                    messages.success(request, _("Ticket closed successfully."))
                elif action == 'open' and ticket.is_closed:
                    ticket.is_closed = False
                    ticket.closed_at = None
                    ticket.save()
                    messages.success(request, _("Ticket reopened successfully."))
                else:
                    messages.info(request, _("No changes made to ticket status."))
            except Exception as e:
                messages.error(request, _("Error updating ticket status: {}").format(str(e)))
        
        # Redirect back to chat view
        return HttpResponseRedirect(
            reverse(f"admin:{self.model._meta.app_label}_{self.model._meta.model_name}_chat_detail", args=[object_id])
        )
    
    def edit_message_view(self, request, object_id, message_id):
        """Handle editing message from chat interface."""
        from django.contrib import messages
        from django.shortcuts import get_object_or_404
        from django.http import HttpResponseRedirect, JsonResponse
        from django.urls import reverse
        import json
        
        if request.method == 'POST':
            ticket = get_object_or_404(Ticket, pk=object_id)
            message = get_object_or_404(TicketMessage, pk=message_id, ticket=ticket)
            
            # Check if user has permission to edit this message
            if not self.has_change_permission(request, ticket):
                if request.headers.get('Content-Type') == 'application/json':
                    return JsonResponse({
                        'success': False,
                        'error': _("You don't have permission to edit this message.")
                    })
                messages.error(request, _("You don't have permission to edit this message."))
                return HttpResponseRedirect(
                    reverse(f"admin:{self.model._meta.app_label}_{self.model._meta.model_name}_chat_detail", args=[object_id])
                )
            
            # Only allow editing text messages
            if message.message_type != TicketMessage.MessageType.TEXT:
                if request.headers.get('Content-Type') == 'application/json':
                    return JsonResponse({
                        'success': False,
                        'error': _("Only text messages can be edited.")
                    })
                messages.error(request, _("Only text messages can be edited."))
                return HttpResponseRedirect(
                    reverse(f"admin:{self.model._meta.app_label}_{self.model._meta.model_name}_chat_detail", args=[object_id])
                )
            
            try:
                # Get new content from request
                if request.content_type == 'application/json':
                    data = json.loads(request.body)
                    new_content = data.get('content', '').strip()
                else:
                    new_content = request.POST.get('content', '').strip()
                
                if not new_content:
                    error_msg = _("Message content cannot be empty.")
                    if request.content_type == 'application/json':
                        return JsonResponse({'success': False, 'error': error_msg})
                    messages.error(request, error_msg)
                    return HttpResponseRedirect(
                        reverse(f"admin:{self.model._meta.app_label}_{self.model._meta.model_name}_chat_detail", args=[object_id])
                    )
                
                # Update the message
                message.content = new_content
                message.save()
                
                success_msg = _("Message updated successfully.")
                if request.content_type == 'application/json':
                    return JsonResponse({
                        'success': True,
                        'message': success_msg,
                        'new_content': new_content
                    })
                
                messages.success(request, success_msg)
                
            except Exception as e:
                error_msg = _("Error updating message: {}").format(str(e))
                if request.content_type == 'application/json':
                    return JsonResponse({'success': False, 'error': error_msg})
                messages.error(request, error_msg)
        
        # Redirect back to chat view for non-AJAX requests
        return HttpResponseRedirect(
            reverse(f"admin:{self.model._meta.app_label}_{self.model._meta.model_name}_chat_detail", args=[object_id])
        )

class TicketAdmin(BaseTicketAdmin):
    def get_queryset(self, request):
        qs = super().get_queryset(request)
        return qs.filter(ticket_type=Ticket.TicketType.TICKET)

class ReportTicketAdmin(BaseTicketAdmin):
    list_display = ('subject', 'user', 'region',  'is_closed', 'formatted_created_at', 'messages_count')

    def get_queryset(self, request):
        qs = super().get_queryset(request)
        return qs.filter(ticket_type=Ticket.TicketType.REPORT)

# @admin.register(TicketMessage)
class TicketMessageAdmin(RegionFilteredAdmin):
    list_display = ('ticket', 'display_user', 'message_type', 'content_preview', 'is_read', 'created_at')
    list_filter = ('is_read', 'message_type',
        ('created_at', RangeDateFilter),
    )
    search_fields = ('content', 'user__fullname', 'ticket__subject')
    readonly_fields = ('created_at',)
    ordering = ('-created_at',)

    @display(description=TicketMessage._meta.get_field('created_at').verbose_name, ordering='created_at')
    def created_at(self, obj):
        from utils import gregorian_to_jalali
        return gregorian_to_jalali(obj.created_at, "%Y/%m/%d %H:%M") if obj.created_at else "-"

    @display(
        description=_("Content Preview"),
        ordering="content",
    )
    def content_preview(self, obj):
        if obj.message_type == TicketMessage.MessageType.IMAGE:
            if obj.content_image:
                return format_html(
                    '<img src="{}" style="max-width: 50px; max-height: 50px;"> {}',
                    obj.content_image.url,
                    obj.content[:50] + '...' if len(obj.content) > 50 else obj.content
                )
            return _("Image (no file)")
        else:
            return obj.content[:100] + '...' if len(obj.content) > 100 else obj.content
    
    @display(
        description=_("User"),
        ordering="user__fullname",
    )
    def display_user(self, obj):
        """نمایش نام کاربر بر اساس نقش در صندوق"""
        try:
            # بررسی نقش کاربر در صندوق مربوط به تیکت
            user_membership = obj.user.deposit_memberships.filter(deposit=obj.ticket.deposit).first()
            
            if user_membership and user_membership.role in ['Owner', 'Admin']:
                # اگر کاربر مالک یا ادمین صندوق است
                return format_html('<span style="color: #1976d2; font-weight: bold;">{} ({})</span>', 
                                  obj.user.fullname, user_membership.role)
            else:
                # اگر کاربر نقش دیگری دارد یا عضو صندوق نیست
                return format_html('<span>{}</span>', obj.user.fullname)
        except Exception as e:
            # در صورت بروز خطا، نام کاربر را به صورت عادی نمایش می‌دهیم
            return obj.user.fullname
        
        
project_admin_site.register(Ticket, TicketAdmin)
project_admin_site.register(ReportTicket, ReportTicketAdmin)
project_admin_site.register(TicketMessage, TicketMessageAdmin)
