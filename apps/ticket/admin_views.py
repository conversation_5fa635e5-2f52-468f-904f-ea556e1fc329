from django.views.generic import TemplateView
from django.shortcuts import get_object_or_404
from django.urls import reverse
from django.utils.translation import gettext_lazy as _, gettext as __
from django.db.models import OuterRef, Subquery, Q
from django.utils import timezone
from unfold.views import UnfoldModelAdminViewMixin

from .models import Ticket, TicketMessage
from apps.deposit.models import DepositMembership


class TicketChatDetailView(UnfoldModelAdminViewMixin, TemplateView):
    """Custom chat-style detail view for tickets using UnfoldModelAdminViewMixin"""
    title = _("Ticket Chat")
    permission_required = ('ticket.view_ticket',)
    template_name = "admin/ticket/ticket/chat_detail_view.html"
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        object_id = self.kwargs.get('object_id')
        ticket = get_object_or_404(self.model_admin.model, pk=object_id)
        
        # Get all messages for this ticket ordered by creation time
        messages = TicketMessage.objects.filter(
            ticket=ticket
        ).select_related('user').order_by('created_at')
        
        # Mark appropriate messages as read based on user role (before preparing display data)
        messages_to_mark_read = self._get_messages_to_mark_as_read(messages, self.request.user, ticket)
        unread_count = messages_to_mark_read.count()
        
        # به‌روزرسانی وضعیت خوانده‌شدن پیام‌های مناسب
        if unread_count > 0:
            updated_count = messages_to_mark_read.update(is_read=True)
            print(f"Admin view marked {updated_count} messages as read for user {self.request.user.id} in ticket {ticket.id}")
        
        # Prepare messages data for chat interface (after marking as read)
        chat_messages = self.prepare_chat_messages(messages, ticket)
        
        # Get ticket participants info (similar to API logic)
        participants = self.get_ticket_participants(ticket)
        
        # Prepare ticket header info
        ticket_info = self.prepare_ticket_header_info(ticket)
        
        # URLs for actions
        original_change_url = reverse(
            'admin:ticket_ticket_original_change',
            args=[ticket.pk],
            current_app=self.model_admin.admin_site.name
        )
        
        context.update({
            'ticket': ticket,
            'ticket_info': ticket_info,
            'chat_messages': chat_messages,
            'participants': participants,
            'original_change_url': original_change_url,
            'messages_count': messages.count(),
            'unread_messages_count': 0,  # همه پیام‌ها حالا خوانده شده‌اند
            'marked_as_read_count': unread_count,  # تعداد پیام‌هایی که به عنوان خوانده شده علامت‌گذاری شدند
        })
        
        return context
    
    def prepare_chat_messages(self, messages, ticket):
        """
        Prepare messages for chat interface display
        """
        chat_messages = []
        current_user = self.request.user
        
        for message in messages:
            # Determine message direction (sent/received)
            is_sent = message.user == current_user
            
            # Get user role in ticket context
            user_role = self.get_user_role_in_ticket(message.user, ticket)
            
            # Get sender info using the same logic as serializer
            sender_info = self.get_sender_info(message.user, ticket)
            sender_type = self.get_sender_type(message.user, ticket)
            
            # Get user avatar URL
            avatar_url = None
            if hasattr(message.user, 'avatar') and message.user.avatar:
                avatar_url = self.request.build_absolute_uri(message.user.avatar.url)
            
            # Prepare message data
            message_data = {
                'id': message.id,
                'content': message.content,
                'message_type': message.message_type,
                'content_image': message.content_image,
                'created_at': message.created_at,
                'is_read': message.is_read,
                'is_sent': is_sent,
                'user': {
                    'id': message.user.id,
                    'fullname': message.user.fullname,
                    'role': user_role,
                    'avatar_url': avatar_url,
                },
                'sender_type': sender_type,
                'sender_info': sender_info,
            }
            
            chat_messages.append(message_data)
        
        return chat_messages
    
    def get_ticket_participants(self, ticket):
        """Get ticket participants info similar to API logic"""
        participants = {
            "sender": {},
            "receiver": {}
        }

        # Ticket creator (always sender)
        ticket_creator = ticket.user
        participants["sender"] = {
            "id": ticket_creator.id,
            "fullname": ticket_creator.fullname,
            "role": "Ticket Creator",
            "type": "user"
        }

        # Determine receiver based on ticket type
        if ticket.ticket_type == 'report':
            # For report tickets, receiver is technical support
            participants["receiver"] = {
                "id": 0,  # Virtual ID for support
                "fullname": __("Support"),
                "role": "Support",
                "type": "support"
            }
        elif ticket.deposit:
            # For deposit tickets, managers are receivers
            manager = DepositMembership.objects.filter(
                deposit=ticket.deposit,
                role__in=[DepositMembership.Role.ADMIN, DepositMembership.Role.OWNER],
                is_active=True
            ).select_related('user').first()

            if manager:
                participants["receiver"] = {
                    "id": manager.user.id,
                    "fullname": ticket.deposit.title,  # Show deposit name
                    "role": manager.role,
                    "type": "manager"
                }
            else:
                participants["receiver"] = participants["sender"].copy()
        else:
            # For general tickets, receiver is general support
            participants["receiver"] = {
                "id": 0,  # Virtual ID for support                                                                                                                                          
                "fullname": __("General Support"),
                "role": "Support",
                "type": "support"
            }

        return participants
    
    def prepare_ticket_header_info(self, ticket):
        """Prepare ticket header information for chat interface"""
        return {
            "id": ticket.id,
            "subject": ticket.subject,
            "ticket_type": ticket.get_ticket_type_display(),
            "is_closed": ticket.is_closed,
            "created_at": ticket.created_at,
            "closed_at": ticket.closed_at,
            "region_name": ticket.region.name if ticket.region else None,
            "deposit_name": ticket.deposit.title if ticket.deposit else None,
        }
    
    def get_user_role_in_ticket(self, user, ticket):
        """Determine user role in ticket context"""
        if ticket.user == user:
            return "Ticket Creator"

        if ticket.deposit:
            membership = DepositMembership.objects.filter(
                user=user,
                deposit=ticket.deposit,
                is_active=True
            ).first()
            if membership:
                return membership.get_role_display()

        return "Member"
    
    def get_sender_type(self, user, ticket):
        """Determine sender type (user, manager, support)"""
        # For report type tickets
        if ticket.ticket_type == 'report':
            if user == ticket.user:
                return "user"
            else:
                return "support"
        
        # For regular tickets with deposit
        if ticket.deposit:
            membership = DepositMembership.objects.filter(
                user=user,
                deposit=ticket.deposit,
                role__in=[DepositMembership.Role.ADMIN, DepositMembership.Role.OWNER],
                is_active=True
            ).first()
            if membership:
                return "manager"
        
        # Check if user is admin/support (you might need to adjust this logic)
        if user.is_superuser or user.is_staff:
            return "support"
            
        return "user"
    
    def get_sender_info(self, user, ticket):
        """Get sender information based on their role and ticket type"""
        # For report type tickets
        if ticket.ticket_type == 'report':
            if user == ticket.user:
                # Message from the user who created the report
                return {
                    "id": user.id,
                    "name": user.fullname,
                    "role": "User",
                    "type": "user"
                }
            else:
                # Message from support/admin
                return {
                    "id": user.id,
                    "name": __("Technical Support"),
                    "role": "Support",
                    "type": "support"
                }

        # For regular ticket type
        if ticket.deposit:
            membership = DepositMembership.objects.filter(
                user=user,
                deposit=ticket.deposit,
                is_active=True
            ).first()
            role = membership.role if membership else None

            if role in [DepositMembership.Role.ADMIN, DepositMembership.Role.OWNER]:
                # For managers, show deposit title and their role
                return {
                    "id": user.id,
                    "name": ticket.deposit.title,
                    "role": role,
                    "type": "manager"
                }
            else:
                # For users, show their actual name
                return {
                    "id": user.id,
                    "name": user.fullname,
                    "role": role or "Member",
                    "type": "user"
                }

        # Fallback
        return {
            "id": user.id,
            "name": user.fullname,
            "role": "User",
            "type": "user"
        }
    
    def _get_messages_to_mark_as_read(self, queryset, user, ticket):
        """
        تشخیص پیام‌هایی که باید به‌عنوان خوانده‌شده علامت‌گذاری شوند
        بر اساس نقش کاربر فعلی در تیکت
        """
        from django.db.models import Q
        
        # ابتدا پیام‌های خود کاربر را حذف کنیم
        messages_queryset = queryset.exclude(user=user).filter(is_read=False)
        
        # برای تیکت‌های گزارش
        if ticket.ticket_type == 'report':
            if user.is_superuser or user.is_staff:
                # اگر کاربر فعلی پشتیبانی است، همه پیام‌های کاربران را بخواند
                return messages_queryset.exclude(user__is_superuser=True).exclude(user__is_staff=True)
            else:
                # اگر کاربر عادی است، پیام‌های پشتیبانی را بخواند
                return messages_queryset.filter(Q(user__is_superuser=True) | Q(user__is_staff=True))
        
        # برای تیکت‌های مربوط به صندوق
        if ticket.deposit:
            # تشخیص نقش کاربر فعلی در صندوق
            current_user_membership = DepositMembership.objects.filter(
                user=user,
                deposit=ticket.deposit,
                is_active=True
            ).first()
            
            current_user_role = current_user_membership.role if current_user_membership else None
            
            # اگر کاربر فعلی پشتیبانی/superuser است
            if user.is_superuser or user.is_staff:
                return messages_queryset
            
            # اگر کاربر فعلی مدیر یا مالک است
            if current_user_role in [DepositMembership.Role.ADMIN, DepositMembership.Role.OWNER]:
                # فقط پیام‌های کاربران عادی (غیر مدیر/مالک) را بخواند
                admin_owner_users = DepositMembership.objects.filter(
                    deposit=ticket.deposit,
                    role__in=[DepositMembership.Role.ADMIN, DepositMembership.Role.OWNER],
                    is_active=True
                ).values_list('user_id', flat=True)
                
                return messages_queryset.exclude(user_id__in=admin_owner_users)
            else:
                # اگر کاربر عادی است، پیام‌های مدیران و مالکان را بخواند
                admin_owner_users = DepositMembership.objects.filter(
                    deposit=ticket.deposit,
                    role__in=[DepositMembership.Role.ADMIN, DepositMembership.Role.OWNER],
                    is_active=True
                ).values_list('user_id', flat=True)
                
                return messages_queryset.filter(user_id__in=admin_owner_users)
        
        # برای تیکت‌های عمومی (بدون صندوق)
        if user.is_superuser or user.is_staff:
            # پشتیبانی همه پیام‌ها را می‌خواند
            return messages_queryset
        else:
            # کاربر عادی فقط پیام‌های پشتیبانی را می‌خواند
            return messages_queryset.filter(Q(user__is_superuser=True) | Q(user__is_staff=True))