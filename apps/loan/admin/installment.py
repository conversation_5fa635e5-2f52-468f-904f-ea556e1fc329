from django.contrib import admin
from django.utils.translation import gettext_lazy as _
from django import forms
from django.utils.html import format_html
from datetime import date

from unfold.admin import ModelAdmin, TabularInline
from unfold.contrib.filters.admin import (
    RangeNumericFilter, 
    RangeDateFilter, 
    BooleanRadioFilter
)
from unfold.decorators import display, action

from apps.loan.models import LoanInstallment
from utils.admin import project_admin_site

class LoanInstallmentInline(TabularInline):
    model = LoanInstallment
    extra = 0
    readonly_fields = ('installment_number', 'created_at', 'updated_at')
    fields = ('installment_number', 'amount', 'due_date', 'is_paid', 'paid_date')
    can_delete = False
    verbose_name = _("Loan Installment")
    verbose_name_plural = _("Loan Installments")
    ordering = ('installment_number',)
    hide_title = True  # Unfold specific option to hide title row
    
    def has_add_permission(self, request, obj=None):
        return False

class LoanInstallmentForm(forms.ModelForm):
    class Meta:
        model = LoanInstallment
        exclude = ()

class LoanInstallmentAdmin(ModelAdmin):
    form = LoanInstallmentForm
    list_display = (
        'installment_info', 'payment_status_badge', 'installment_amount', 'user_info', 'due_date'
    )
    list_filter = (
        ('is_paid', BooleanRadioFilter),
        ('due_date', RangeDateFilter),
        ('amount', RangeNumericFilter),
        'loan__status', 
        'loan__deposit'
    )
    search_fields = ('loan__deposit__title', 'loan__deposit_membership__user__fullname')
    readonly_fields = ('created_at', 'updated_at')
    compressed_fields = False    
    list_filter_submit = True  # Enable submit button for filters
    
    fieldsets = (
        (None, {
            'fields': ('loan', 'installment_number', 'amount')
        }),
        (_('Payment Details'), {
            'fields': ('due_date', 'is_paid', 'paid_date'),
            'classes': ['tab'],
        }),
        (_('Metadata'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ['tab'],
        }),
    )
    ordering = ('loan', 'installment_number')
    date_hierarchy = 'due_date'
    
    def get_queryset(self, request):
        """Optimize queries by prefetching related objects"""
        return super().get_queryset(request).select_related(
            'loan', 'loan__deposit', 'loan__deposit_membership', 'loan__deposit_membership__user'
        )
    
    @display(description='Installment', header=True)
    def installment_info(self, obj):
        loan_link = format_html(
            '<a href="/admin/loan/loan/{}/change/">Loan #{}</a>', 
            obj.loan.id, obj.loan.id
        )
        
        deposit_name = obj.loan.deposit.title if obj.loan.deposit else _('No Deposit')
        
        return [
            f"{_('Installment')} #{obj.installment_number}",
            loan_link,
            None,
        ]
    
    @display(description='Status', label={
        True: 'success',   # Paid installments in green
        False: 'danger',   # Unpaid installments in red
    })
    def payment_status_badge(self, obj):
        if obj.is_paid:
            return True, _('Paid')
        elif obj.due_date < date.today():
            return False, _('Overdue')
        else:
            return False, _('Pending')
    
    @display(description='Amount')
    def installment_amount(self, obj):
        formatted_amount = f"{obj.amount:,}" if obj.amount else "0"
        return f"{formatted_amount} {_('Toman')}"
    
    @display(description='User', header=True)
    def user_info(self, obj):
        user = obj.loan.deposit_membership.user if obj.loan.deposit_membership else None
        user_name = user.fullname if user else _('No User')
        
        return [
            user_name,
            str(user.phone_number) if user and user.phone_number else "-",
            None,
        ]
    
    @display(description=LoanInstallment._meta.get_field('due_date').verbose_name, ordering='due_date')
    def due_date(self, obj):
        from utils import gregorian_to_jalali
        return gregorian_to_jalali(obj.due_date, "%Y/%m/%d %H:%M") if obj.due_date else "-"
    
    @action(description=_("Mark as Paid"))
    def mark_as_paid(self, request, queryset):
        """Mark selected installments as paid"""
        count = 0
        for installment in queryset.filter(is_paid=False):
            installment.mark_as_paid()
            count += 1
            
            # Update loan status after marking installment as paid
            installment.loan.update_status()
        
        self.message_user(
            request,
            _("Successfully marked {} installments as paid.").format(count),
        )
    
    @action(description=_("Mark as Unpaid"))
    def mark_as_unpaid(self, request, queryset):
        """Mark selected installments as unpaid"""
        count = 0
        for installment in queryset.filter(is_paid=True):
            installment.is_paid = False
            installment.paid_date = None
            installment.save()
            count += 1
            
            # Update loan status after marking installment as unpaid
            installment.loan.update_status()
        
        self.message_user(
            request,
            _("Successfully marked {} installments as unpaid.").format(count),
        )

# Register with both admin sites
admin.site.register(LoanInstallment, LoanInstallmentAdmin)
project_admin_site.register(LoanInstallment, LoanInstallmentAdmin)