from django.contrib import admin
from unfold.admin import ModelAdmin
from .models import AppVersion
from utils.admin import project_admin_site


@admin.register(AppVersion)
class AppVersionAdmin(ModelAdmin):
    list_display = ['version', 'is_active', 'jalali_created_at', 'apk_file_size']
    list_filter = ['is_active', 'created_at']
    search_fields = ['version', 'description']
    readonly_fields = ['created_at', 'updated_at']
    ordering = ['-created_at']

    from unfold.decorators import display
    @display(description=AppVersion._meta.get_field('created_at').verbose_name, ordering='created_at')
    def jalali_created_at(self, obj):
        from utils import gregorian_to_jalali
        return gregorian_to_jalali(obj.created_at, "%Y/%m/%d %H:%M") if obj.created_at else "-"

    fieldsets = (
        ('اطلاعات اصلی', {
            'fields': ('version', 'apk_file', 'is_active')
        }),
        ('توضیحات', {
            'fields': ('description',),
            'classes': ('collapse',)
        }),
        ('تاریخ‌ها', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def apk_file_size(self, obj):
        """Display APK file size in a readable format"""
        if obj.apk_file:
            size = obj.apk_file.size
            if size < 1024:
                return f"{size} B"
            elif size < 1024 * 1024:
                return f"{size / 1024:.1f} KB"
            else:
                return f"{size / (1024 * 1024):.1f} MB"
        return "فایل موجود نیست"

    apk_file_size.short_description = 'حجم فایل'

    def save_model(self, request, obj, form, change):
        """Override save to ensure only one active version at a time if needed"""
        super().save_model(request, obj, form, change)

        # Optional: If you want only one active version at a time, uncomment below
        # if obj.is_active:
        #     AppVersion.objects.exclude(pk=obj.pk).update(is_active=False)


# Register with project_admin_site
project_admin_site.register(AppVersion, AppVersionAdmin)

# Register with default admin site (already done with @admin.register decorator above)

# project_admin_site.register(AppVersion, AppVersionAdmin)
