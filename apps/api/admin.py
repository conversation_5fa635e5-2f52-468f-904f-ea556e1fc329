from django.contrib import admin
from django.utils.translation import gettext_lazy as _
from utils.admin import project_admin_site
from .models import Guide, GuideContent


class GuideContentInline(admin.TabularInline):
    """
    Inline برای محتوای راهنما
    """
    model = GuideContent
    extra = 1
    fields = ['title', 'description', 'order']
    ordering = ['order', 'id']


@admin.register(Guide, site=project_admin_site)
class GuideAdmin(admin.ModelAdmin):
    """
    ادمین مدل راهنما
    """
    list_display = ['page_name', 'field_name', 'is_active', 'contents_count', 'jalali_created_at']
    list_filter = ['is_active', 'created_at', 'page_name']
    search_fields = ['page_name', 'field_name']
    readonly_fields = ['page_name', 'field_name', 'created_at', 'updated_at']

    from unfold.decorators import display
    @display(description=Guide._meta.get_field('created_at').verbose_name, ordering='created_at')
    def jalali_created_at(self, obj):
        from utils import gregorian_to_jalali
        return gregorian_to_jalali(obj.created_at, "%Y/%m/%d %H:%M") if obj.created_at else "-"

    fieldsets = (
        (_('Basic Information'), {
            'fields': ('page_name', 'field_name', 'is_active')
        }),
        (_('Timestamps'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    inlines = [GuideContentInline]

    def contents_count(self, obj):
        """نمایش تعداد محتواها"""
        return obj.contents.count()
    contents_count.short_description = _('Contents Count')

    def has_add_permission(self, request):
        """غیرفعال کردن امکان اضافه کردن دستی"""
        return False

    def has_delete_permission(self, request, obj=None):
        """غیرفعال کردن امکان حذف"""
        return False


@admin.register(GuideContent, site=project_admin_site)
class GuideContentAdmin(admin.ModelAdmin):
    """
    ادمین مدل محتوای راهنما
    """
    list_display = ['guide', 'title', 'order', 'jalali_created_at']
    list_filter = ['guide__page_name', 'guide__is_active', 'created_at']
    search_fields = ['guide__page_name', 'guide__field_name', 'title', 'description']
    list_editable = ['order']

    from unfold.decorators import display
    @display(description=GuideContent._meta.get_field('created_at').verbose_name, ordering='created_at')
    def jalali_created_at(self, obj):
        from utils import gregorian_to_jalali
        return gregorian_to_jalali(obj.created_at, "%Y/%m/%d %H:%M") if obj.created_at else "-"

    fieldsets = (
        (_('Guide Information'), {
            'fields': ('guide',)
        }),
        (_('Content'), {
            'fields': ('title', 'description', 'order')
        }),
        (_('Timestamps'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    readonly_fields = ['created_at', 'updated_at']
