from django.utils.translation import gettext_lazy as _
from django.contrib import admin
from django.db.models import Q
from django.utils.html import format_html
from django import forms
from django.core.exceptions import ValidationError
from django.urls import reverse

from unfold.admin import ModelAdmin
from unfold.decorators import display, action
from unfold.contrib.filters.admin import (
    RangeNumericFilter, 
    RangeDateFilter, 
    BooleanRadioFilter,
    ChoicesRadioFilter
)

from apps.request.models import (
    RequestCreateDeposit, 
    RequestJoinDeposit, 
    LoanRequest, 
    WithdrawalRequest
)
from utils.admin import project_admin_site
from utils import gregorian_to_jala<PERSON>, create_deposit_view_field_with_button


class BaseRequestAdminMixin(ModelAdmin):
    """Base mixin for request admin classes with common functionality"""
    list_filter_submit = True  # Enable submit button for filters
    ordering = ('-created_at',)
    readonly_fields = ('created_at', 'updated_at')
    search_fields = ('user__fullname', 'deposit__name')
    date_hierarchy = 'created_at'
    
    @display(description=_('Status'), label={
        'pending': 'warning',
        'approved': 'success',
        'rejected': 'danger',
        'cancelled': 'secondary',
    })
    def request_status(self, obj):
        status_map = {
            'pending': _('Pending'),
            'approved': _('Approved'),
            'rejected': _('Rejected'),
            'cancelled': _('Cancelled'),
        }
        return obj.status, status_map.get(obj.status, obj.status)
    
    @display(description=_('Created'))
    def formatted_created_at(self, obj):
        return gregorian_to_jalali(obj.created_at, date_format="%Y/%m/%d") if obj.created_at else "-"
    
    @display(description=_('User'), header=True)
    def user_info(self, obj):
        if hasattr(obj, 'user') and obj.user:
            return [
                obj.user.fullname,
                str(obj.user.phone_number) if obj.user.phone_number else "",
            ]
        elif hasattr(obj, 'deposit_membership') and obj.deposit_membership and obj.deposit_membership.user:
            return [
                obj.deposit_membership.user.fullname,
                str(obj.deposit_membership.user.phone_number) if obj.deposit_membership.user.phone_number else "",
            ]
        return ["-", ""]
    
    @display(description=_('Deposit'))
    def deposit_info(self, obj):
        if obj.deposit:
            return obj.deposit.title
        return "-"
    
    @action(description=_("Approve Request"))
    def approve_request(self, request, queryset):
        for obj in queryset:
            obj.approve()
        self.message_user(request, _("Selected requests have been approved."))
    
    @action(description=_("Reject Request"))
    def reject_request(self, request, queryset):
        for obj in queryset:
            obj.reject()
        self.message_user(request, _("Selected requests have been rejected."))
    
    def save_model(self, request, obj, form, change):
        status_field = 'status'
        status_choices = getattr(obj, 'StatusChoices', None) or getattr(obj, 'LoanStatus', None) or getattr(obj, 'RequestStatus', None)
        
        if status_choices and hasattr(obj, status_field):
            current_status = getattr(obj, status_field)
            if current_status == status_choices.APPROVED:
                obj.approve()
            elif current_status == status_choices.REJECTED:
                obj.reject()
            else:
                super().save_model(request, obj, form, change)
        else:
            super().save_model(request, obj, form, change)

    def has_delete_permission(self, request, obj=None):
        return False
class RequestCreateDepositForm(forms.ModelForm):
    """Custom form for RequestCreateDeposit admin"""
    class Meta:
        model = RequestCreateDeposit
        fields = '__all__'
    
    def clean(self):
        cleaned_data = super().clean()
        status = cleaned_data.get('status')
        rejection_reason = cleaned_data.get('rejection_reason')
        
        # If status is rejected, rejection_reason is required
        if status == RequestCreateDeposit.StatusChoices.REJECTED and not rejection_reason:
            raise ValidationError({
                'rejection_reason': _('Rejection reason is required when status is rejected.')
            })
        
        return cleaned_data



class RequestCreateDepositAdmin(BaseRequestAdminMixin):
    """Admin for deposit creation requests"""
    form = RequestCreateDepositForm
    list_display = (
        'user_info', 'deposit_info', 'request_status', 'formatted_created_at'
    )
    readonly_fields = ['created_at', 'updated_at', 'deposit_view_field', 'user']    
    list_filter = (
        ('status', ChoicesRadioFilter),
        ('created_at', RangeDateFilter),
        ('updated_at', RangeDateFilter),
    )
    fieldsets = (
        (None, {
            'fields': ('deposit_view_field', 'user', 'status', 'rejection_reason'),
        }),
        (_('Timestamps'), {
            'fields': ('created_at', 'updated_at'),
            # 'classes': ('tab',),
        }),
    )
    actions = ['approve_request', 'reject_request']
    # Define conditional fields - show rejection_reason only when status is rejected
    conditional_fields = {
        'rejection_reason': 'status === "rejected"'
    }
    
    @display(description=_('Deposit'))
    def deposit_view_field(self, obj):
        """Display deposit with view button"""
        if obj.deposit:
            # Map Deposit.DepositType to admin URL names
            deposit_type_mapping = {
                'Poll': 'polldeposit',
                'Saving': 'savingdeposit', 
                'Reporting': 'reportingdeposit',
            }
            
            deposit_type = deposit_type_mapping.get(obj.deposit.deposit_type, 'polldeposit')
            
            return create_deposit_view_field_with_button(
                deposit_name=obj.deposit.title,
                deposit_id=obj.deposit.id,
                deposit_type=deposit_type,
                label="مشاهده صندوق"
            )
        return "-"


    
    
class RequestJoinDepositForm(forms.ModelForm):
    """Custom form for RequestJoinDeposit admin"""
    class Meta:
        model = RequestJoinDeposit
        fields = '__all__'
    
    def clean(self):
        cleaned_data = super().clean()
        status = cleaned_data.get('status')
        rejection_reason = cleaned_data.get('rejection_reason')
        
        # If status is rejected, rejection_reason is required
        if status == RequestJoinDeposit.StatusChoices.REJECTED and not rejection_reason:
            raise ValidationError({
                'rejection_reason': _('Rejection reason is required when status is rejected.')
            })
        
        return cleaned_data


class RequestJoinDepositAdmin(BaseRequestAdminMixin):
    """Admin for deposit join requests"""
    form = RequestJoinDepositForm
    list_display = (
        'user_info', 'deposit_info', 'request_status', 'requested_unit_count', 
        'monthly_installment_amount', 'formatted_created_at'
    )
    list_filter = (
        ('status', ChoicesRadioFilter),
        ('created_at', RangeDateFilter),
        ('updated_at', RangeDateFilter),
        ('requested_unit_count', RangeNumericFilter),
        ('monthly_installment_amount', RangeNumericFilter),
    )
    fieldsets = (
        (None, {
            'fields': ('deposit', 'user'),
        }),
        (_('Request Details'), {
            'fields': ('status', 'requested_unit_count', 'monthly_installment_amount', 'rejection_reason'),
            'classes': ('tab',),
        }),
        (_('Timestamps'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('tab',),
        }),
    )
    actions = ['approve_request', 'reject_request']
    
    # Define conditional fields - show rejection_reason only when status is rejected
    conditional_fields = {
        'rejection_reason': 'status === "rejected"'
    }
    
    @display(description=_('Requested Units'))
    def requested_unit_count(self, obj):
        return obj.requested_unit_count
    
    @display(description=_('Monthly Installment'))
    def monthly_installment_amount(self, obj):
        if obj.monthly_installment_amount:
            formatted_amount = f'{int(obj.monthly_installment_amount):,} {_("Toman")}'
            return format_html('<span class="font-medium">{}</span>', formatted_amount)
        return 0


class LoanRequestForm(forms.ModelForm):
    """Custom form for LoanRequest admin"""
    class Meta:
        model = LoanRequest
        fields = '__all__'
    
    def clean(self):
        cleaned_data = super().clean()
        status = cleaned_data.get('status')
        rejection_reason = cleaned_data.get('rejection_reason')
        
        # If status is rejected, rejection_reason is required
        if status == LoanRequest.LoanStatus.REJECTED and not rejection_reason:
            raise ValidationError({
                'rejection_reason': _('Rejection reason is required when status is rejected.')
            })
        
        return cleaned_data


class LoanRequestAdmin(BaseRequestAdminMixin):
    """Admin for loan requests"""
    form = LoanRequestForm
    list_display = (
        'user_info', 'deposit_info', 'amount', 'installment_count', 
        'installment_date', 'request_status', 'formatted_created_at'
    )
    list_filter = (
        ('status', ChoicesRadioFilter),
        ('created_at', RangeDateFilter),
        ('updated_at', RangeDateFilter),
        ('amount', RangeNumericFilter),
        ('installment_count', RangeNumericFilter),
    )
    fieldsets = (
        (None, {
            'fields': ('deposit', 'user'),
        }),
        (_('Loan Details'), {
            'fields': ('amount', 'installment_count', 'installment_date', 'description'),
            'classes': ('tab',),
        }),
        (_('Status'), {
            'fields': ('status', 'rejection_reason'),
            'classes': ('tab',),
        }),
        (_('Timestamps'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('tab',),
        }),
    )
    actions = ['approve_request', 'reject_request']
    
    # Define conditional fields - show rejection_reason only when status is rejected
    conditional_fields = {
        'rejection_reason': 'status === "rejected"'
    }
    
    @display(description=_('Loan Amount'))
    def amount(self, obj):
        if obj.amount:
            formatted_amount = f'{int(obj.amount):,} {_("Toman")}'
            return format_html('<span class="font-medium">{}</span>', formatted_amount)
        return 0
    
    @display(description=_('Installments'))
    def installment_count(self, obj):
        return obj.installment_count
    
    @display(description=LoanRequest._meta.get_field('installment_date').verbose_name, ordering='installment_date')
    def installment_date(self, obj):
        from utils import gregorian_to_jalali
        return gregorian_to_jalali(obj.installment_date, "%Y/%m/%d %H:%M") if obj.installment_date else "-"


class WithdrawalRequestForm(forms.ModelForm):
    """Custom form for WithdrawalRequest admin"""
    class Meta:
        model = WithdrawalRequest
        fields = '__all__'
    
    def clean(self):
        cleaned_data = super().clean()
        status = cleaned_data.get('status')
        rejection_reason = cleaned_data.get('rejection_reason')
        
        # If status is rejected, rejection_reason is required
        if status == WithdrawalRequest.RequestStatus.REJECTED and not rejection_reason:
            raise ValidationError({
                'rejection_reason': _('Rejection reason is required when status is rejected.')
            })
        
        return cleaned_data


class WithdrawalRequestAdmin(BaseRequestAdminMixin):
    """Admin for withdrawal requests"""
    form = WithdrawalRequestForm
    list_display = (
        'user_info', 'deposit_info', 'account_holder_name', 'amount', 
        'iban_display', 'request_status', 'formatted_created_at'
    )
    list_filter = (
        ('status', ChoicesRadioFilter),
        ('created_at', RangeDateFilter),
        ('updated_at', RangeDateFilter),
        ('amount', RangeNumericFilter),
    )
    search_fields = (
        'deposit_membership__user__fullname', 'deposit__name', 
        'account_holder_name', 'iban', 'reason', 'rejection_reason'
    )
    fieldsets = (
        (_('Request Information'), {
            'fields': ('deposit', 'deposit_membership', 'account_holder_name', 'amount', 'iban', 'reason'),
        }),
        (_('Status'), {
            'fields': ('status', 'rejection_reason'),
            'classes': ('tab',),
        }),
        (_('Timestamps'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('tab',),
        }),
    )
    actions = ['approve_request', 'reject_request']
    
    # Define conditional fields - show rejection_reason only when status is rejected
    conditional_fields = {
        'rejection_reason': 'status === "rejected"'
    }
    
    @display(description=_('Withdrawal Amount'))
    def amount(self, obj):
        if obj.amount:
            formatted_amount = f'{int(obj.amount):,} {_("Toman")}'
            return format_html('<span class="font-medium">{}</span>', formatted_amount)
        return 0
    
    @display(description=_('Account Holder'))
    def account_holder_name(self, obj):
        return obj.account_holder_name
    
    @display(description=_('IBAN'))
    def iban_display(self, obj):
        # Display IBAN with formatting for better readability
        if obj.iban:
            masked_iban = f"{obj.iban[:4]}...{obj.iban[-4:]}"
            return format_html('<code>{}</code>', masked_iban)
        return "-"


# Register models with the project_admin_site
project_admin_site.register(RequestCreateDeposit, RequestCreateDepositAdmin)
project_admin_site.register(RequestJoinDeposit, RequestJoinDepositAdmin)
project_admin_site.register(LoanRequest, LoanRequestAdmin)
project_admin_site.register(WithdrawalRequest, WithdrawalRequestAdmin)

# Register with default admin site
admin.site.register(RequestCreateDeposit, RequestCreateDepositAdmin)
admin.site.register(RequestJoinDeposit, RequestJoinDepositAdmin)
admin.site.register(LoanRequest, LoanRequestAdmin)
admin.site.register(WithdrawalRequest, WithdrawalRequestAdmin)